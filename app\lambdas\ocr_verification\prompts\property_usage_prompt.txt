## Task Summary:
Extract the selected property usage option from the provided image.
Please do not answer immediately, think step by step and show me your thinking.

## Context Information:
- The image contains a "Property Usage" section
- In this section, there are selection options that may appear as radio buttons (circles) or checkboxes (squares)
- Only one option is selected/checked
- The selected option has either a filled/darkened circle, a checkmark (✓), a half/quarter filled circle, or an X mark inside its indicator

## Model Instructions:
- First, locate the "Property Usage" section in the document
- Then, identify all selection options present in this section (typically Industrial, Commercial, and Residential)
- Next, determine which option is selected by looking for:
  * Radio buttons: A filled/darkened circle
  * Checkboxes: A checkmark (✓), X mark, or filled square
- The selected option will have a visible mark inside its indicator, while unselected options will have empty indicators
- Pay special attention to the spatial relationship between the indicators and their labels
- Think step-by-step about which indicator appears marked versus which ones appear empty
- Be aware that selection indicators may appear as circles or squares
- Output should be in English language only, DO NOT include Arabic words

## Response style and format requirements:
- Return only the selected property usage value in the JSON structure
- Show your thinking in <scratchpad> tags
- Do not include explanations or additional information outside the specified tags
- Ensure the response is the exact label text as it appears in the document
- Return the option in its original language and case as shown in the document
- In <json> tag, return only the valid JSON output according to the required structure, without any extra explanation or formatting.
- Return both tags <scratchpad> and <json> in your response.
- The <json> tag must contain **only valid JSON** as specified.

## Strictly Follow Output Format mentioned below:
Format the output as a JSON object with the following structure:
```json
{{
  "business_rules": {{
	"property_usage": "[selected property usage value exactly as it appears in document only in ENGLIST if found otherwise return empty]"
  }}
}}
```