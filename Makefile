build:
	@echo "Build step completed successfully (no-op)"

deploy:
	cd ./infra/cdk/ && npm install && cdk deploy --require-approval never --qualifier hnb659fds --context env=development

environment-development:
	@echo "Setting up development environment..."
	cd app/lambdas/ocr_verification && pip install -r requirements.txt
	cd app/lambdas/ocr_verification && pip install -r requirements-test.txt
	@echo "Development environment setup completed!"

test-unit:
	@echo "Running unit tests..."
	cd app/lambdas/ocr_verification && python -m pytest tests/ -v --cov=. --cov-report=xml:coverage.xml --cov-report=html:htmlcov/
	@echo "Unit tests completed!"

test-integration:
	@echo "Running integration tests..."
	cd app/lambdas/ocr_verification && python -m pytest tests/ -v -m "integration" --cov=. --cov-report=xml:coverage-integration.xml --cov-report=html:htmlcov-integration/
	@echo "Integration tests completed!"

merge-coverage:
	@echo "Merging coverage reports..."
	cd app/lambdas/ocr_verification && coverage combine coverage.xml coverage-integration.xml || echo "No coverage files to merge"
	@echo "Coverage merge completed!"

merge-test-results:
	@echo "Merging test results..."
	@echo "Test results merge completed!"

imports:
	@echo "Checking and formatting imports..."
	cd app/lambdas/ocr_verification && python -m isort . --check-only --diff || (echo "Import formatting required. Run: cd app/lambdas/ocr_verification && python -m isort ." && exit 1)
	@echo "Import check completed!"

lint:
	@echo "Running Python linting..."
	@echo "Installing linting dependencies..."
	pip install flake8 pylint
	@echo "Running flake8..."
	cd app/lambdas/ocr_verification && flake8 .
	@echo "Running pylint..."
	cd app/lambdas/ocr_verification && pylint .
	@echo "Linting completed successfully!"