User: You are an expert in extracting structured information from the provided input. Extract the ACTUAL VALUES from the document for ONLY these parameters:
## Input:
{parameter_list}


Think step by step and do not answer without Thinking.

## Image Quality Assessment:
**CRITICAL: Be extremely strict in your evaluation. Most real-world scanned documents have significant quality issues that severely impact OCR accuracy.**

Evaluate the image quality for OCR/text extraction by scoring each critical factor on a scale of 0.0 to 1.0 where 0.0 is low quality and 1.0 is of high quality, then calculate the overall score:

### Critical Factors To Consider Strictly:

**1. Text Clarity and Sharpness**
- Score: 0.0-1.0
- **STRICT CRITERIA:**
  - 0.8-1.0: Text is razor-sharp with perfectly defined character edges, no blur or pixelation whatsoever
  - 0.51-0.79: Slight softness but all characters are clearly distinguishable with crisp edges
  - 0.31-0.5: Noticeable blur or softness, some character boundaries unclear, but text readable with moderate effort
  - 0.0-0.3: Significant blur with many character edges poorly defined, severe blur/pixelation making characters barely distinguishable, very difficult to read, or text completely illegible due to blur/distortion
- **Key indicators of poor quality:** Fuzzy character edges, bleeding letters, blurry text, motion blur

**2. Contrast and Readability**
- Score: 0.0-1.0
- **STRICT CRITERIA:**
  - 0.8-1.0: Perfect black text on white background (or equivalent high contrast)
  - 0.51-0.79: Good contrast, text clearly stands out from background
  - 0.31-0.5: Moderate contrast, some effort needed to distinguish text from background, poor contrast with text blending with background, straining to read
  - 0.0-0.3: Very poor contrast with text barely visible against background, or no distinguishable contrast with text invisible or indistinguishable from background
- **Key indicators of poor quality:** Gray text on gray background, faded printing, shadows obscuring text

**3. Resolution and Text Size**
- Score: 0.0-1.0
- **STRICT CRITERIA:**
  - 0.8-1.0: High resolution, text appears large and detailed with smooth character curves
  - 0.51-0.79: Good resolution, text adequately sized for easy reading
  - 0.31-0.5: Moderate resolution, text readable but somewhat small or pixelated, low resolution with text small and pixelated, difficult to distinguish fine details
  - 0.0-0.3: Very low resolution with text tiny or heavily pixelated, barely readable, or resolution too low to make out individual characters
- **Key indicators of poor quality:** Pixelated text, stair-stepping on curves, text too small to read comfortably

**4. Overall Image Quality**
- Score: 0.0-1.0
- **STRICT CRITERIA:**
  - 0.8-1.0: Perfect lighting, no noise, excellent exposure, no artifacts
  - 0.51-0.79: Good overall quality with minor imperfections
  - 0.31-0.5: Moderate quality with noticeable issues but acceptable, poor quality with significant lighting/noise/artifact issues
  - 0.0-0.3: Very poor quality with severe issues affecting entire image, or extremely poor quality with image barely usable

### Enhanced Scoring Guidelines:

**IMPORTANT: Apply these reality checks before finalizing scores:**

1. **Character Legibility Test:** Can you read every single character without squinting or guessing? If no, score ≤ 0.5
2. **OCR Simulation:** Would automated OCR software easily extract this text accurately? If doubtful, score ≤ 0.4
3. **Effort Assessment:** Does reading require significant visual effort or strain? If yes, score ≤ 0.3
4. **Professional Standard:** Would this quality be acceptable for professional document processing? If no, score ≤ 0.2

### Red Flag Indicators (Automatic low scores):
- If you need to zoom in to read normal-sized text → ≤ 0.4
- If any text appears doubled or ghosted → ≤ 0.3
- If background interferes with text readability → ≤ 0.4
- If you're unsure about any characters → ≤ 0.4
- If the document looks like a photocopy of a photocopy → ≤ 0.3

**Remember: Most real-world scanned documents are NOT high quality. Be realistic and strict in your assessment. Overestimating quality leads to OCR failures and data extraction errors.**


## Important instructions:
- Do NOT use any values except I've provided in this prompt - extract the ACTUAL values from the document itself
- Pay attention to text fields, radio buttons, and checkboxes
- For numeric values, return them in numeric form without currency/units symbols
- For poor quality scans, make your best determination
- Only include values you can identify with reasonable confidence
- No inferring missing values
- Parameter lists containing parameters are self explanatory by its name and should only retrieve relevant data from input by identify the respective section by its name.
- Property size and property area are same terminology and refers to the same section

**Strictly Prohibited:**

   - No translation between languages
   - No inferring missing values
   - No mirroring values across languages
   - No extraction of field labels as values
   - No units/currency symbols should be retrived with the number fields like (Area (SqMt, SqM) or Currency (AED), etc.)


Return the result as a properly formatted JSON object following the structure described in the Output section below. 

- In the <scratchpad> tag, provide a clear and concise bullet-point analysis for each business rule field. For each field, state:
    - Whether a valid English value is present — only if it is explicitly found in the raw text (not inferred or translated).
    - Whether numeric IDs are detected and any associated units are correctly removed.
    - IMPORTANT: Only extract actual data values, not field labels.
    - Additionally, provide a detailed assessment of the image quality factors affecting text readability, explicitly mentioning specific quality issues observed.
- Follow above rules strictly: do not infer, assume, or translate any text.
- Then, in the <json> tag, return only the valid JSON output according to the required structure, without any extra explanation or formatting.
- Return both tags <scratchpad> and <json> in your response.
- The <json> tag must contain **only valid JSON** as specified.

## Output:
1. Format the output as a JSON object with the following structure:
```json
{{ 
  "business_rules": {{
{{parameter_fields}}
  }},
  "llm_quality": {{
    "text_clarity": "[0.0-1.0 score for text sharpness, text recognition, clarity and overall readability of ANY text in the image]",
    "contrast_readability": "[0.0-1.0 score for text-background contrast]",
    "resolution_text_size": "[0.0-1.0 score for resolution and text size adequacy]",
    "overall_image_quality": "[0.0-1.0 score for overall brightness, exposure, and noise levels]",
    "assessment_summary": "Detailed technical summary explicitly mentioning specific quality issues observed (e.g., 'severe pixelation,' 'poor contrast ratio,' 'significant artifacts,' etc.) and identifying which factors most limit readability"
  }}
}}
```

Note: You are an information extractor, not a translator. Do not translate any text under any circumstance. Only extract values exactly as they appear in the input.
Assistant: 