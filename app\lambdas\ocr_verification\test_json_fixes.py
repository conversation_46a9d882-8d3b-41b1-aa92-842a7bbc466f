#!/usr/bin/env python3
"""
Test script to validate JSON parsing fixes and dual LLM verification.
This script tests the enhanced error handling and validation mechanisms.
"""

import os
import sys
import json
import time
from pathlib import Path

# Set test environment
os.environ['ENV'] = 'test'

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.process import _validate_and_parse_json, _clean_json_content, extract_answer_content
from core.llm.gemini import <PERSON><PERSON><PERSON>
from dual_llm_processor import DualLLMProcessor
from core.logger.logger import get_logger, log_frame_info, log_frame_error
from core.enums.enums import ResponseStatus

logger = get_logger(__name__)

def test_json_validation():
    """Test the enhanced JSON validation functions."""
    log_frame_info(logger, "🧪 Testing JSON validation functions...")
    
    test_cases = [
        # Valid JSON
        ('{"business_rules": {"policy_number": "12345"}}', True),
        
        # JSON with line breaks in strings
        ('{"business_rules": {"assessment": "This is a\nmultiline string"}}', True),
        
        # JSON with escaped quotes
        ('{"business_rules": {"description": "He said \\"hello\\""}}', True),
        
        # Malformed JSON - missing comma
        ('{"business_rules": {"field1": "value1" "field2": "value2"}}', False),
        
        # Malformed JSON - trailing comma
        ('{"business_rules": {"field1": "value1",}}', True),  # Should be fixed by cleaning
        
        # Empty JSON
        ('{}', True),
        
        # Non-JSON string
        ('This is not JSON', False),
        
        # JSON with control characters
        ('{"business_rules": {"field": "value\x00with\x01control"}}', True),  # Should be cleaned
    ]
    
    passed = 0
    failed = 0
    
    for i, (json_str, should_pass) in enumerate(test_cases):
        try:
            log_frame_info(logger, f"Test case {i+1}: {json_str[:50]}...")
            
            # Test JSON cleaning
            cleaned = _clean_json_content(json_str)
            log_frame_info(logger, f"Cleaned: {cleaned[:50]}...")
            
            # Test validation
            result = _validate_and_parse_json(cleaned, {"inputTokens": 0, "outputTokens": 0})
            
            success = result.get("status") == ResponseStatus.SUCCESS.value
            
            if success == should_pass:
                log_frame_info(logger, f"✅ Test case {i+1} passed")
                passed += 1
            else:
                log_frame_error(logger, f"❌ Test case {i+1} failed - Expected {should_pass}, got {success}")
                failed += 1
                
        except Exception as e:
            log_frame_error(logger, f"❌ Test case {i+1} threw exception: {e}")
            failed += 1
    
    log_frame_info(logger, f"JSON validation tests: {passed} passed, {failed} failed")
    return failed == 0

def test_gemini_function_validation():
    """Test Gemini function call argument validation."""
    log_frame_info(logger, "🧪 Testing Gemini function call validation...")
    
    test_args = [
        # Valid args
        {"policy_number": "12345", "claim_amount": "1000.00"},
        
        # Args with problematic characters
        {"description": "Text with\nnewlines and\ttabs"},
        
        # Args with quotes
        {"statement": 'He said "hello" to me'},
        
        # Nested structure
        {"business_rules": {"field1": "value1", "field2": "value2"}},
        
        # Mixed types
        {"number": 123, "boolean": True, "null_value": None, "string": "text"},
    ]
    
    passed = 0
    failed = 0
    
    for i, args in enumerate(test_args):
        try:
            log_frame_info(logger, f"Testing args case {i+1}: {args}")
            
            # Test validation and sanitization
            sanitized = GeminiLlm._validate_and_sanitize_function_args(args)
            
            if sanitized is not None:
                # Try to serialize to JSON
                json_str = json.dumps(sanitized, ensure_ascii=False)
                log_frame_info(logger, f"✅ Args case {i+1} passed - JSON length: {len(json_str)}")
                passed += 1
            else:
                log_frame_error(logger, f"❌ Args case {i+1} failed - sanitization returned None")
                failed += 1
                
        except Exception as e:
            log_frame_error(logger, f"❌ Args case {i+1} threw exception: {e}")
            failed += 1
    
    log_frame_info(logger, f"Gemini validation tests: {passed} passed, {failed} failed")
    return failed == 0

def test_dual_llm_comparison():
    """Test the dual LLM comparison functionality."""
    log_frame_info(logger, "🧪 Testing dual LLM comparison...")
    
    processor = DualLLMProcessor()
    
    # Test cases for comparison
    test_cases = [
        # Identical results
        {
            "bedrock": {
                "status": ResponseStatus.SUCCESS.value,
                "extracted_claims": {"policy_number": "12345", "claim_amount": "1000.00"}
            },
            "gemini": {
                "status": ResponseStatus.SUCCESS.value,
                "extracted_claims": {"policy_number": "12345", "claim_amount": "1000.00"}
            },
            "expected_consistency": True
        },
        
        # Different values
        {
            "bedrock": {
                "status": ResponseStatus.SUCCESS.value,
                "extracted_claims": {"policy_number": "12345", "claim_amount": "1000.00"}
            },
            "gemini": {
                "status": ResponseStatus.SUCCESS.value,
                "extracted_claims": {"policy_number": "54321", "claim_amount": "2000.00"}
            },
            "expected_consistency": False
        },
        
        # One failed
        {
            "bedrock": {
                "status": ResponseStatus.SUCCESS.value,
                "extracted_claims": {"policy_number": "12345"}
            },
            "gemini": {
                "status": ResponseStatus.FAIL.value,
                "message": "Processing failed"
            },
            "expected_consistency": False
        }
    ]
    
    passed = 0
    failed = 0
    
    for i, case in enumerate(test_cases):
        try:
            log_frame_info(logger, f"Testing comparison case {i+1}")
            
            comparison = processor.compare_results(case["bedrock"], case["gemini"])
            
            actual_consistency = comparison["overall_consistency"]
            expected_consistency = case["expected_consistency"]
            
            if actual_consistency == expected_consistency:
                log_frame_info(logger, f"✅ Comparison case {i+1} passed")
                passed += 1
            else:
                log_frame_error(logger, f"❌ Comparison case {i+1} failed - Expected {expected_consistency}, got {actual_consistency}")
                failed += 1
                
        except Exception as e:
            log_frame_error(logger, f"❌ Comparison case {i+1} threw exception: {e}")
            failed += 1
    
    log_frame_info(logger, f"Dual LLM comparison tests: {passed} passed, {failed} failed")
    return failed == 0

def main():
    """Run all tests."""
    log_frame_info(logger, "🚀 Starting JSON parsing and dual LLM validation tests...")
    
    start_time = time.time()
    
    # Run all test suites
    test_results = [
        test_json_validation(),
        test_gemini_function_validation(),
        test_dual_llm_comparison()
    ]
    
    total_time = time.time() - start_time
    
    # Summary
    passed_suites = sum(test_results)
    total_suites = len(test_results)
    
    log_frame_info(logger, f"📊 TEST SUMMARY:")
    log_frame_info(logger, f"  ✅ Passed test suites: {passed_suites}/{total_suites}")
    log_frame_info(logger, f"  ⏱️ Total time: {total_time:.2f}s")
    
    if passed_suites == total_suites:
        log_frame_info(logger, "🎉 All tests passed! JSON fixes are working correctly.")
        return True
    else:
        log_frame_error(logger, f"❌ {total_suites - passed_suites} test suite(s) failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
