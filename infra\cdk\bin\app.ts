#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { OcrVerificationStack } from '../lib/stack';

const app = new cdk.App();

// Get environment from context or environment variable
const deploymentEnv = app.node.tryGetContext('env') || process.env.DEPLOYMENT_ENVIRONMENT || 'development';

// Define account configurations
const accountConfigs = {
  development: {
    accountId: '************',
    region: 'ap-southeast-1'
  },
  staging: {
    accountId: '************',
    region: 'ap-southeast-1'
  },
  production: {
    accountId: '************',
    region: 'ap-southeast-1'
  }
};

// Select the appropriate configuration
const config = accountConfigs[deploymentEnv as keyof typeof accountConfigs] || accountConfigs.staging;

// Create stack name based on environment
const getStackName = (env: string): string => {
  switch (env) {
    case 'development':
      return 'OcrVerificationDevelopmentStack';
    case 'staging':
      return 'OcrVerificationStagingStack';
    case 'production':
      return 'OcrVerificationProductionStack';
    default:
      return 'OcrVerificationStagingStack'; // Default fallback
  }
};

// Create the stack
new OcrVerificationStack(app, getStackName(deploymentEnv), {
  env: {
    account: config.accountId,
    region: config.region
  },
  envName: deploymentEnv // Pass the environment to the stack using the renamed property
});

// Add tags to all resources in the app
cdk.Tags.of(app).add('Application', 'OcrVerification');
cdk.Tags.of(app).add('Environment', deploymentEnv);