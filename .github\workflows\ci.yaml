name: ci

on:
  push:
    paths-ignore:
      - "**.md"
      - LICENCE
    branches:
      - master
  pull_request:

permissions:
  id-token: write
  contents: read

env:
  DEFAULT_GO_VERSION: '1.23'
  GH_ACCESS_TOKEN: ${{ secrets.SUBMODULES_TOKEN }}
  AWS_DEFAULT_REGION: ap-southeast-1
  ENV: development

jobs:
  tests:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS creds
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.PF_GITHUB_ACTIONS_AWS_ROLE_ARN }}
          role-duration-seconds: 3600
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Configure GIT
        run: git config --global url.https://$<EMAIL>/.insteadOf https://github.com/

      - name: Install GO
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.DEFAULT_GO_VERSION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Test environment setup
        run: |
          make environment-development

      - name: Unit tests
        run: make test-unit

      - name: Functional tests
        run: make test-integration

      - name: Merge coverage and test results
        run: |
          make merge-coverage
          make merge-test-results

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.SUBMODULES_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  static-checks:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - name: Install GO
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.DEFAULT_GO_VERSION }}
      - name: Checkout
        uses: actions/checkout@v2
      - name: Configure GIT
        run: git config --global url.https://$<EMAIL>/.insteadOf https://github.com/
      - name: Linter
        run: make lint
      - name: Imports
        run: |
          make imports
          git diff --exit-code || (echo "Files were changed by goimports-reviser. Please format the code." && exit 1)

  lint-api-docs:
    uses: Propertyfinder/alpha-ci-cd/.github/workflows/oas-validate.yaml@master
    secrets: inherit