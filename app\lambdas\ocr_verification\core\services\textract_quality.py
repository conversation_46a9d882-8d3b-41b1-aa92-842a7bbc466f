import boto3
import json
import math
import time
from io import BytesIO
from PIL import Image
from pdf2image import convert_from_bytes
from config.config import config
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_error,
    log_frame_debug,
)
from core.enums.enums import ResponseStatus

# Initialize logger
logger = get_logger(__name__)

# Load configuration
config_obj = config.ssm_config

# Initialize Textract client
textract_client = boto3.client('textract', region_name=config_obj.get('aws_region', 'us-east-1'))


def calculate_confidence_metrics(confidences):
    """
    Calculate various confidence metrics from a list of confidence scores.
    
    Args:
        confidences (list): List of confidence scores (0.0-1.0)
        
    Returns:
        dict: Dictionary containing various confidence metrics
    """
    if not confidences:
        return {
            "geometric_mean": 0.0,
            "arithmetic_mean": 0.0,
            "harmonic_mean": 0.0,
            "minimum": 0.0,
            "maximum": 0.0,
            "median": 0.0,
            "std_deviation": 0.0,
            "total_words": 0
        }
    
    n = len(confidences)
    
    # Convert to percentages for easier interpretation
    confidences_percent = [conf * 100.0 for conf in confidences]
    
    # Arithmetic mean (simple average)
    arithmetic_mean = sum(confidences_percent) / n
    
    # Geometric mean (best for combining probabilities)
    product = 1.0
    for conf in confidences:
        product *= conf
    geometric_mean = (product ** (1.0 / n)) * 100.0
    
    # Harmonic mean (more conservative)
    harmonic_sum = sum(1.0 / conf for conf in confidences if conf > 0)
    harmonic_mean = n / harmonic_sum if harmonic_sum > 0 else 0.0
    
    # Min, max, median
    sorted_confs = sorted(confidences_percent)
    minimum = min(confidences_percent)
    maximum = max(confidences_percent)
    median = sorted_confs[n // 2] if n % 2 == 1 else (sorted_confs[n // 2 - 1] + sorted_confs[n // 2]) / 2
    
    # Standard deviation
    variance = sum((conf - arithmetic_mean) ** 2 for conf in confidences_percent) / n
    std_deviation = math.sqrt(variance)
    
    return {
        "geometric_mean": round(geometric_mean, 2),
        "arithmetic_mean": round(arithmetic_mean, 2),
        "harmonic_mean": round(harmonic_mean, 2),
        "minimum": round(minimum, 2),
        "maximum": round(maximum, 2),
        "median": round(median, 2),
        "std_deviation": round(std_deviation, 2),
        "total_words": n
    }


def assess_document_quality_with_textract(image_bytes):
    """
    Assess document quality using AWS Textract OCR confidence scores.
    
    Args:
        image_bytes: Image data to assess
        
    Returns:
        dict: Quality assessment results with confidence scores and routing decision
    """
    try:
        start_time = time.time()
        
        # Convert image to PIL Image for processing
        image = Image.open(BytesIO(image_bytes))
        
        # Convert image to bytes for Textract
        img_byte_arr = BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        log_frame_info(logger, f"Processing image with Textract (size: {len(img_byte_arr)} bytes)")
        
        # Call Textract detect_document_text
        response = textract_client.detect_document_text(
            Document={'Bytes': img_byte_arr}
        )
        
        processing_time = time.time() - start_time
        log_frame_info(logger, f"Textract processing completed in {processing_time:.2f} seconds")
        
        # Extract confidence scores from Textract response
        confidences = []
        words_data = []
        
        for block in response['Blocks']:
            if block['BlockType'] == 'WORD':
                confidence = block.get('Confidence', 0) / 100.0  # Convert percentage to decimal
                confidences.append(confidence)
                
                words_data.append({
                    "text": block.get('Text', ''),
                    "confidence": confidence * 100.0,  # Keep as percentage for output
                    "bbox": {
                        "left": block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0),
                        "top": block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0),
                        "width": block.get('Geometry', {}).get('BoundingBox', {}).get('Width', 0),
                        "height": block.get('Geometry', {}).get('BoundingBox', {}).get('Height', 0)
                    }
                })
        
        # Calculate confidence metrics
        confidence_metrics = calculate_confidence_metrics(confidences)
        
        # Determine quality assessment based on confidence scores
        # Use geometric mean as primary metric (most reliable for OCR confidence)
        primary_confidence = confidence_metrics['geometric_mean']
        
        # Quality assessment logic based on confidence scores
        if primary_confidence >= 90:
            quality_level = "excellent"
            quality_score = 1.0
        elif primary_confidence >= 80:
            quality_level = "good"
            quality_score = 0.8
        elif primary_confidence >= 70:
            quality_level = "fair"
            quality_score = 0.6
        elif primary_confidence >= 60:
            quality_level = "poor"
            quality_score = 0.4
        else:
            quality_level = "very_poor"
            quality_score = 0.2
        
        # Routing decision based on confidence threshold
        # Default threshold: 60% geometric mean confidence
        confidence_threshold = float(config_obj.get("textract_confidence_threshold", 60))
        quality_acceptable = primary_confidence >= confidence_threshold
        
        assessment_summary = (
            f"Textract OCR analysis: {primary_confidence:.1f}% geometric mean confidence. "
            f"Quality level: {quality_level}. "
            f"Total words detected: {confidence_metrics['total_words']}. "
            f"Confidence range: {confidence_metrics['minimum']:.1f}%-{confidence_metrics['maximum']:.1f}%"
        )
        
        if not quality_acceptable:
            assessment_summary += f" Document quality below threshold ({confidence_threshold}%)."
        
        result = {
            "status": ResponseStatus.SUCCESS.value,
            "result": {
                "textract_confidence": {
                    "geometric_mean": primary_confidence,
                    "arithmetic_mean": confidence_metrics['arithmetic_mean'],
                    "harmonic_mean": confidence_metrics['harmonic_mean'],
                    "minimum": confidence_metrics['minimum'],
                    "maximum": confidence_metrics['maximum'],
                    "median": confidence_metrics['median'],
                    "std_deviation": confidence_metrics['std_deviation'],
                    "total_words": confidence_metrics['total_words']
                },
                "quality_assessment": {
                    "quality_level": quality_level,
                    "quality_score": quality_score,
                    "quality_acceptable": quality_acceptable,
                    "confidence_threshold": confidence_threshold,
                    "assessment_summary": assessment_summary
                },
                "words_data": words_data[:100]  # Limit to first 100 words for performance
            },
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": int(processing_time * 1000),
            },
        }
        
        log_frame_info(logger, f"Quality assessment completed: {quality_level} ({primary_confidence:.1f}% confidence)")
        return result
        
    except Exception as e:
        log_frame_error(logger, f"Error in Textract quality assessment: {str(e)}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"Textract quality assessment failed: {str(e)}",
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": 0,
            },
        }


def assess_pdf_quality_with_textract(pdf_bytes):
    """
    Assess PDF document quality using AWS Textract by converting to images first.
    
    Args:
        pdf_bytes: PDF document bytes
        
    Returns:
        dict: Quality assessment results for the entire PDF
    """
    try:
        start_time = time.time()
        
        # Convert PDF to images
        images = convert_from_bytes(pdf_bytes, dpi=200)
        
        if not images:
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Failed to convert PDF to images",
                "llm_usage": {
                    "inputTokens": 0,
                    "outputTokens": 0,
                    "totalTokens": 0,
                    "latencyMs": 0,
                },
            }
        
        log_frame_info(logger, f"Processing {len(images)} pages with Textract")
        
        # Process each page
        page_assessments = []
        all_confidences = []
        total_words = 0
        
        for page_num, image in enumerate(images):
            # Convert image to bytes
            img_byte_arr = BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            
            # Assess page quality
            page_result = assess_document_quality_with_textract(img_bytes)
            
            if page_result["status"] == ResponseStatus.SUCCESS.value:
                page_data = page_result["result"]
                page_confidences = page_data["textract_confidence"]
                
                page_assessments.append({
                    "page": page_num + 1,
                    "confidence": page_confidences["geometric_mean"],
                    "quality_level": page_data["quality_assessment"]["quality_level"],
                    "word_count": page_confidences["total_words"]
                })
                
                # Collect all confidences for overall assessment
                # We'll need to reconstruct the individual word confidences
                # For now, use the geometric mean as representative
                all_confidences.extend([page_confidences["geometric_mean"] / 100.0] * page_confidences["total_words"])
                total_words += page_confidences["total_words"]
            else:
                log_frame_error(logger, f"Failed to assess page {page_num + 1}: {page_result.get('message', 'Unknown error')}")
        
        if not page_assessments:
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Failed to assess any pages",
                "llm_usage": {
                    "inputTokens": 0,
                    "outputTokens": 0,
                    "totalTokens": 0,
                    "latencyMs": 0,
                },
            }
        
        # Calculate overall confidence metrics
        overall_confidence_metrics = calculate_confidence_metrics(all_confidences)
        primary_confidence = overall_confidence_metrics['geometric_mean']
        
        # Determine overall quality
        if primary_confidence >= 90:
            quality_level = "excellent"
            quality_score = 1.0
        elif primary_confidence >= 80:
            quality_level = "good"
            quality_score = 0.8
        elif primary_confidence >= 70:
            quality_level = "fair"
            quality_score = 0.6
        elif primary_confidence >= 60:
            quality_level = "poor"
            quality_score = 0.4
        else:
            quality_level = "very_poor"
            quality_score = 0.2
        
        # Routing decision
        confidence_threshold = float(config_obj.get("textract_confidence_threshold", 60))
        quality_acceptable = primary_confidence >= confidence_threshold
        
        total_time = time.time() - start_time
        
        assessment_summary = (
            f"PDF Textract analysis: {primary_confidence:.1f}% overall geometric mean confidence. "
            f"Quality level: {quality_level}. "
            f"Total pages: {len(page_assessments)}, Total words: {total_words}. "
            f"Confidence range: {overall_confidence_metrics['minimum']:.1f}%-{overall_confidence_metrics['maximum']:.1f}%"
        )
        
        if not quality_acceptable:
            assessment_summary += f" Document quality below threshold ({confidence_threshold}%)."
        
        result = {
            "status": ResponseStatus.SUCCESS.value,
            "result": {
                "textract_confidence": overall_confidence_metrics,
                "quality_assessment": {
                    "quality_level": quality_level,
                    "quality_score": quality_score,
                    "quality_acceptable": quality_acceptable,
                    "confidence_threshold": confidence_threshold,
                    "assessment_summary": assessment_summary
                },
                "page_assessments": page_assessments
            },
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": int(total_time * 1000),
            },
        }
        
        log_frame_info(logger, f"PDF quality assessment completed: {quality_level} ({primary_confidence:.1f}% confidence)")
        return result
        
    except Exception as e:
        log_frame_error(logger, f"Error in PDF Textract quality assessment: {str(e)}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"PDF Textract quality assessment failed: {str(e)}",
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": 0,
            },
        } 