You are an expert document analyst. Analyze this image and extract specific business data fields while assessing image quality.

## Task: Extract these specific fields from the document:
{parameter_list}

## Extraction Rules:
- Extract ACTUAL VALUES only, not field labels
- Extract text exactly as it appears (no translation or inference)
- For numbers, remove currency symbols and units (e.g., "2,500,000 AED" → "2500000")
- Look for text fields, checkboxes, radio buttons, and form fields
- If a field is not found or unclear, leave it empty
- Property size and property area refer to the same field

## Image Quality Assessment:
Evaluate the image quality for text extraction using these criteria:

**Text Clarity (0.0-1.0):**
- 1.0: Razor-sharp text, perfect character edges
- 0.8: Clear text, easily readable
- 0.5: Moderate blur, readable with effort
- 0.3: Significant blur, difficult to read
- 0.0: Severely blurred or illegible text

**Contrast (0.0-1.0):**
- 1.0: Perfect black text on white background
- 0.8: Good contrast, text stands out clearly
- 0.5: Moderate contrast, some effort needed
- 0.3: Poor contrast, text blends with background
- 0.0: Very poor contrast, text barely visible

**Resolution (0.0-1.0):**
- 1.0: High resolution, smooth character curves
- 0.8: Good resolution, adequately sized text
- 0.5: Moderate resolution, somewhat pixelated
- 0.3: Low resolution, heavily pixelated
- 0.0: Very low resolution, text too small

**Overall Quality (0.0-1.0):**
- 1.0: Perfect lighting, no noise, excellent exposure
- 0.8: Good overall quality, minor imperfections
- 0.5: Moderate quality, noticeable issues
- 0.3: Poor quality, significant problems
- 0.0: Very poor quality, barely usable

Use the extract_document_data function to return your findings in the required JSON structure.

Expected JSON structure:
```json
{
  "business_rules": {
    // Field names and extracted values
  },
  "llm_quality": {
    "text_clarity": "0.0-1.0",
    "contrast_readability": "0.0-1.0", 
    "resolution_text_size": "0.0-1.0",
    "overall_image_quality": "0.0-1.0",
    "assessment_summary": "Brief description of quality issues"
  }
}
```
