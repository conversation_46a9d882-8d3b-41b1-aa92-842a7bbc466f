#!/usr/bin/env python3
"""
Production-ready dual LLM test that bypasses S3 storage issues.
Processes the test PDF through both Bedrock and Gemini with direct image processing.
"""

import os
import sys
import json
import time
from typing import Dict, List, Any

# Set test environment to avoid SSM issues
os.environ['ENV'] = 'test'

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.lambda_ocr_service import assess_pdf_quality_with_lambda_ocr
from core.services.process import extract_parameters_from_image, verify_claims
from core.llm.llm_factory import LlmFactory
from core.utils.utils import load_pdf_with_text, convert_pdf_to_images
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_warning
from core.enums.enums import ResponseStatus
from config.config import config

# Initialize logger
logger = get_logger(__name__)


class ProductionDualLLMTest:
    """
    Production-ready test that processes PDF through both LLM providers with direct image processing.
    """
    
    def __init__(self):
        """Initialize the test processor."""
        self.bedrock_provider = None
        self.gemini_provider = None
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize both LLM providers."""
        try:
            # Clear any cached providers
            LlmFactory.clear_cache()
            
            # Initialize Bedrock provider
            from core.llm.bedrock import BedrockLlm
            self.bedrock_provider = BedrockLlm
            log_frame_info(logger, "✅ Bedrock LLM provider initialized")
            
            # Initialize Gemini provider
            from core.llm.gemini import GeminiLlm
            self.gemini_provider = GeminiLlm
            log_frame_info(logger, "✅ Gemini LLM provider initialized")
            
        except Exception as e:
            log_frame_error(logger, f"Failed to initialize LLM providers: {str(e)}")
            raise
    
    def assess_document_quality(self, pdf_bytes: bytes) -> Dict[str, Any]:
        """Assess document quality using OCR service."""
        log_frame_info(logger, "🔍 Starting OCR quality assessment...")
        
        try:
            quality_result = assess_pdf_quality_with_lambda_ocr(pdf_bytes)
            
            if quality_result["status"] == ResponseStatus.SUCCESS.value:
                quality_data = quality_result["result"]
                confidence_score = quality_data["rapid_confidence"]["geometric_mean"]
                
                log_frame_info(logger, f"📊 OCR Quality Score: {confidence_score:.3f}")
                
                # Check against quality threshold
                threshold = float(config.ssm_config.get("document_quality_threshold", "0.7"))
                
                if confidence_score >= threshold:
                    log_frame_info(logger, f"✅ Document quality passed threshold ({threshold})")
                    return {
                        "status": "PASSED",
                        "confidence_score": confidence_score,
                        "threshold": threshold,
                        "quality_data": quality_data
                    }
                else:
                    log_frame_warning(logger, f"⚠️ Document quality below threshold ({threshold})")
                    return {
                        "status": "FAILED",
                        "confidence_score": confidence_score,
                        "threshold": threshold,
                        "quality_data": quality_data
                    }
            else:
                return {
                    "status": "ERROR",
                    "message": quality_result.get("message", "OCR quality assessment failed")
                }
                
        except Exception as e:
            log_frame_error(logger, f"Error during quality assessment: {str(e)}")
            return {
                "status": "ERROR",
                "message": f"Quality assessment error: {str(e)}"
            }
    
    def process_image_with_provider(self, image, business_rules: List[str], 
                                  provider_name: str, provider_class) -> Dict[str, Any]:
        """Process a single image with a specific LLM provider."""
        log_frame_info(logger, f"🖼️ Processing image with {provider_name}...")
        
        try:
            start_time = time.time()
            
            # Temporarily override the LLM factory
            original_provider = LlmFactory._provider_instances.get(provider_name.lower())
            LlmFactory._provider_instances[provider_name.lower()] = provider_class
            
            # Set environment variable
            original_env = os.environ.get('LLM_PROVIDER')
            os.environ['LLM_PROVIDER'] = provider_name.lower()
            
            try:
                # Process image directly
                result = extract_parameters_from_image(image, business_rules)
                
                processing_time = time.time() - start_time
                result["processing_time"] = processing_time
                result["provider"] = provider_name
                
                log_frame_info(logger, f"✅ {provider_name} image processing completed in {processing_time:.2f}s")
                return result
                
            finally:
                # Restore original environment and provider cache
                if original_env:
                    os.environ['LLM_PROVIDER'] = original_env
                elif 'LLM_PROVIDER' in os.environ:
                    del os.environ['LLM_PROVIDER']
                
                if original_provider:
                    LlmFactory._provider_instances[provider_name.lower()] = original_provider
                elif provider_name.lower() in LlmFactory._provider_instances:
                    del LlmFactory._provider_instances[provider_name.lower()]
                    
        except Exception as e:
            log_frame_error(logger, f"Error processing image with {provider_name}: {str(e)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"{provider_name} image processing failed: {str(e)}",
                "provider": provider_name,
                "processing_time": time.time() - start_time if 'start_time' in locals() else 0
            }
    
    def compare_extraction_results(self, bedrock_result: Dict[str, Any], 
                                 gemini_result: Dict[str, Any]) -> Dict[str, Any]:
        """Compare extraction results from both providers."""
        log_frame_info(logger, "🔍 Comparing extraction results...")
        
        comparison = {
            "status_match": bedrock_result.get("status") == gemini_result.get("status"),
            "bedrock_status": bedrock_result.get("status"),
            "gemini_status": gemini_result.get("status"),
            "differences": [],
            "similarities": [],
            "overall_consistency": True
        }
        
        # Compare extracted data if both succeeded
        if (bedrock_result.get("status") == ResponseStatus.SUCCESS.value and 
            gemini_result.get("status") == ResponseStatus.SUCCESS.value):
            
            bedrock_data = bedrock_result.get("extracted_data", {})
            gemini_data = gemini_result.get("extracted_data", {})
            
            # Compare business rules
            bedrock_rules = bedrock_data.get("business_rules", {})
            gemini_rules = gemini_data.get("business_rules", {})
            
            all_fields = set(bedrock_rules.keys()) | set(gemini_rules.keys())
            
            for field in all_fields:
                bedrock_value = bedrock_rules.get(field, "NOT_FOUND")
                gemini_value = gemini_rules.get(field, "NOT_FOUND")
                
                if bedrock_value == gemini_value:
                    comparison["similarities"].append({
                        "field": field,
                        "value": bedrock_value,
                        "match": True
                    })
                else:
                    comparison["differences"].append({
                        "field": field,
                        "bedrock_value": bedrock_value,
                        "gemini_value": gemini_value,
                        "match": False
                    })
                    comparison["overall_consistency"] = False
        
        # Log comparison results
        if comparison["overall_consistency"]:
            log_frame_info(logger, "✅ Both LLM providers produced consistent results")
        else:
            log_frame_warning(logger, f"⚠️ Found {len(comparison['differences'])} differences between providers")
            for diff in comparison["differences"]:
                log_frame_warning(logger, f"  - {diff['field']}: Bedrock='{diff['bedrock_value']}', Gemini='{diff['gemini_value']}'")
        
        return comparison
    
    def test_verification_workflow(self, user_claims: Dict[str, str], 
                                 extracted_claims: Dict[str, str]) -> Dict[str, Any]:
        """Test the verification workflow with both providers."""
        log_frame_info(logger, "🔍 Testing verification workflow...")
        
        verification_results = {}
        
        for provider_name, provider_class in [("Bedrock", self.bedrock_provider), ("Gemini", self.gemini_provider)]:
            log_frame_info(logger, f"🔄 Testing verification with {provider_name}...")
            
            try:
                # Temporarily override the LLM factory
                original_provider = LlmFactory._provider_instances.get(provider_name.lower())
                LlmFactory._provider_instances[provider_name.lower()] = provider_class
                
                original_env = os.environ.get('LLM_PROVIDER')
                os.environ['LLM_PROVIDER'] = provider_name.lower()
                
                try:
                    start_time = time.time()
                    result = verify_claims(user_claims, extracted_claims)
                    processing_time = time.time() - start_time
                    
                    result["processing_time"] = processing_time
                    result["provider"] = provider_name
                    verification_results[provider_name.lower()] = result
                    
                    log_frame_info(logger, f"✅ {provider_name} verification completed in {processing_time:.2f}s")
                    
                finally:
                    # Restore environment
                    if original_env:
                        os.environ['LLM_PROVIDER'] = original_env
                    elif 'LLM_PROVIDER' in os.environ:
                        del os.environ['LLM_PROVIDER']
                    
                    if original_provider:
                        LlmFactory._provider_instances[provider_name.lower()] = original_provider
                    elif provider_name.lower() in LlmFactory._provider_instances:
                        del LlmFactory._provider_instances[provider_name.lower()]
                        
            except Exception as e:
                log_frame_error(logger, f"Error in {provider_name} verification: {str(e)}")
                verification_results[provider_name.lower()] = {
                    "status": ResponseStatus.FAIL.value,
                    "message": f"{provider_name} verification failed: {str(e)}",
                    "provider": provider_name
                }
        
        return verification_results

    def run_comprehensive_test(self, pdf_path: str) -> Dict[str, Any]:
        """Run comprehensive dual LLM test with the PDF."""
        log_frame_info(logger, f"🚀 Starting comprehensive dual LLM test for: {pdf_path}")

        try:
            # Load PDF
            with open(pdf_path, 'rb') as f:
                pdf_bytes = f.read()

            log_frame_info(logger, f"📄 Loaded PDF: {len(pdf_bytes)} bytes")

            # Step 1: OCR Quality Assessment
            quality_assessment = self.assess_document_quality(pdf_bytes)

            if quality_assessment["status"] != "PASSED":
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "Document quality assessment failed",
                    "quality_assessment": quality_assessment
                }

            # Step 2: Convert PDF to images
            log_frame_info(logger, "🖼️ Converting PDF to images...")
            images = convert_pdf_to_images(pdf_bytes)

            if not images:
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "Failed to convert PDF to images"
                }

            log_frame_info(logger, f"✅ Converted PDF to {len(images)} images")

            # Step 3: Test extraction with both providers on first image
            business_rules = ["tenant_name", "property_address", "monthly_rent", "lease_start_date", "lease_end_date"]

            bedrock_extraction = self.process_image_with_provider(
                images[0], business_rules, "Bedrock", self.bedrock_provider
            )

            gemini_extraction = self.process_image_with_provider(
                images[0], business_rules, "Gemini", self.gemini_provider
            )

            # Step 4: Compare extraction results
            extraction_comparison = self.compare_extraction_results(bedrock_extraction, gemini_extraction)

            # Step 5: Test verification workflow if extractions succeeded
            verification_results = {}
            if (bedrock_extraction.get("status") == ResponseStatus.SUCCESS.value and
                gemini_extraction.get("status") == ResponseStatus.SUCCESS.value):

                # Use extracted data for verification test
                bedrock_claims = bedrock_extraction.get("extracted_data", {}).get("business_rules", {})
                gemini_claims = gemini_extraction.get("extracted_data", {}).get("business_rules", {})

                # Create test user claims (slightly different for verification testing)
                user_claims = {}
                for key, value in bedrock_claims.items():
                    if key == "monthly_rent" and value:
                        # Test with slightly different amount for verification logic
                        try:
                            amount = float(value.replace(",", "").replace("$", ""))
                            user_claims[key] = str(int(amount * 1.05))  # 5% higher
                        except:
                            user_claims[key] = value
                    else:
                        user_claims[key] = value

                verification_results = self.test_verification_workflow(user_claims, bedrock_claims)

            # Step 6: Generate final report
            final_result = {
                "status": ResponseStatus.SUCCESS.value,
                "message": "Comprehensive dual LLM test completed",
                "quality_assessment": quality_assessment,
                "pdf_info": {
                    "size_bytes": len(pdf_bytes),
                    "total_pages": len(images),
                    "has_extractable_text": bool(load_pdf_with_text(pdf_bytes))
                },
                "extraction_results": {
                    "bedrock": bedrock_extraction,
                    "gemini": gemini_extraction,
                    "comparison": extraction_comparison
                },
                "verification_results": verification_results,
                "summary": {
                    "quality_passed": quality_assessment["status"] == "PASSED",
                    "quality_score": quality_assessment["confidence_score"],
                    "bedrock_extraction_success": bedrock_extraction.get("status") == ResponseStatus.SUCCESS.value,
                    "gemini_extraction_success": gemini_extraction.get("status") == ResponseStatus.SUCCESS.value,
                    "extraction_consistent": extraction_comparison["overall_consistency"],
                    "bedrock_extraction_time": bedrock_extraction.get("processing_time", 0),
                    "gemini_extraction_time": gemini_extraction.get("processing_time", 0),
                    "verification_tested": bool(verification_results)
                }
            }

            # Log comprehensive summary
            log_frame_info(logger, "📊 COMPREHENSIVE TEST SUMMARY:")
            log_frame_info(logger, f"  📋 Quality Score: {final_result['summary']['quality_score']:.3f}")
            log_frame_info(logger, f"  🏗️ Bedrock Extraction: {'✅' if final_result['summary']['bedrock_extraction_success'] else '❌'}")
            log_frame_info(logger, f"  🤖 Gemini Extraction: {'✅' if final_result['summary']['gemini_extraction_success'] else '❌'}")
            log_frame_info(logger, f"  🔍 Results Consistent: {'✅' if final_result['summary']['extraction_consistent'] else '❌'}")
            log_frame_info(logger, f"  ⏱️ Bedrock Time: {final_result['summary']['bedrock_extraction_time']:.2f}s")
            log_frame_info(logger, f"  ⏱️ Gemini Time: {final_result['summary']['gemini_extraction_time']:.2f}s")
            log_frame_info(logger, f"  🔄 Verification Tested: {'✅' if final_result['summary']['verification_tested'] else '❌'}")

            return final_result

        except Exception as e:
            log_frame_error(logger, f"Comprehensive test failed: {str(e)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Comprehensive test failed: {str(e)}"
            }


def main():
    """Main function to run the comprehensive dual LLM test."""
    print("🚀 Starting Production Dual LLM OCR Verification Test")
    print("=" * 70)

    pdf_path = "tenancy-contract-01-bad.pdf"

    try:
        # Initialize test processor
        processor = ProductionDualLLMTest()

        # Run comprehensive test
        result = processor.run_comprehensive_test(pdf_path)

        # Display results
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE TEST RESULTS")
        print("=" * 70)

        if result["status"] == ResponseStatus.SUCCESS.value:
            print("✅ COMPREHENSIVE DUAL LLM TEST SUCCESSFUL")

            summary = result["summary"]
            print(f"\n📋 Test Summary:")
            print(f"  Quality Score: {summary['quality_score']:.3f}")
            print(f"  Bedrock Extraction: {'✅' if summary['bedrock_extraction_success'] else '❌'}")
            print(f"  Gemini Extraction: {'✅' if summary['gemini_extraction_success'] else '❌'}")
            print(f"  Results Consistent: {'✅' if summary['extraction_consistent'] else '❌'}")
            print(f"  Bedrock Time: {summary['bedrock_extraction_time']:.2f}s")
            print(f"  Gemini Time: {summary['gemini_extraction_time']:.2f}s")
            print(f"  Verification Tested: {'✅' if summary['verification_tested'] else '❌'}")

            if not summary['extraction_consistent']:
                print(f"\n⚠️ Found {len(result['extraction_results']['comparison']['differences'])} differences:")
                for diff in result['extraction_results']['comparison']['differences']:
                    print(f"  - {diff['field']}: Bedrock='{diff['bedrock_value']}', Gemini='{diff['gemini_value']}'")

            # Save detailed results
            with open("comprehensive_test_results.json", "w") as f:
                json.dump(result, f, indent=2, default=str)
            print(f"\n💾 Detailed results saved to: comprehensive_test_results.json")

        else:
            print("❌ COMPREHENSIVE TEST FAILED")
            print(f"Error: {result.get('message', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
