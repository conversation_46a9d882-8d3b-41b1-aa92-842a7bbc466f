name: upload-api-definition
on:
  push:
    branches:
      - master
  workflow_dispatch:

permissions:
  id-token: write # required to use OIDC authentication
  contents: read  # required to checkout the code from the repo

jobs:
  upload:
    uses: Propertyfinder/alpha-ci-cd/.github/workflows/upload-api-definition.yml@master
    with:
      SERVICE_NAME: /ocr-verification
    secrets:
      AWS_ROLE_ARN: ${{ secrets.PF_GITHUB_ACTIONS_AWS_ROLE_ARN }}

