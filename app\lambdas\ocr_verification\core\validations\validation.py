from http import H<PERSON><PERSON>tatus
from urllib.parse import urlparse
from core.enums.enums import StatusMessage
from core.logger.logger import get_logger, log_frame_info, log_frame_error
from core.utils.utils import is_empty
from config.config import config

# Initialize logger for this validation module
logger = get_logger(__name__)

# Load configuration object containing SSM parameters
config_obj = config.ssm_config

# Parse allowed document types from configuration
# Extract comma-separated document types, normalize to uppercase, and filter empty values
allowed_document_types_raw = config_obj.get("supported_document_types", "")
ALLOWED_DOCUMENT_TYPES = [
    doc_type.strip().upper()
    for doc_type in allowed_document_types_raw.split(",")
    if doc_type.strip()
]


def build_error_response(input_request, status_code, status, message):
    """
    Build a standardized error response dictionary.

    Args:
        input_request: Original request dictionary containing request metadata
        status_code: HTTP status code for the error
        status: Application-specific status enum value
        message: Human-readable error message

    Returns:
        Dictionary containing standardized error response format
    """
    return {
        "status_code": status_code,
        "status": status,
        "request_id": input_request.get("request_id"),
        "request_type": input_request.get("request_type"),
        "message": message,
    }


def validate_event(input_request):
    """
    Comprehensive validation function for incoming document processing requests.
    Validates required fields, document types, URL formats, and payload content.

    Args:
        input_request: Dictionary containing the incoming request data

    Returns:
        Dictionary with validation results:
        - If valid: {"is_valid": True, "error": None, "request_id": str}
        - If invalid: Error response dict with status_code, status, message, etc.
    """
    log_frame_info(logger, message="Validating event payload")

    # Check if payload field exists and is not empty
    payload = input_request.get("payload", None)
    if not payload:
        log_frame_error(
            logger, message="Validation failed: Missing or empty 'payload' field"
        )
        return build_error_response(
            input_request,
            HTTPStatus.BAD_REQUEST.value,
            StatusMessage.INVALID_REQUEST.value,
            "Payload is missing or empty",
        )

    # Define mandatory fields that must be present in the request
    required_fields = [
        "document_type",  # Type of document being processed
        "proof_document_url",  # URL to the document file
        "request_id",  # Unique identifier for tracking
        "request_type",  # Type of processing requested
    ]

    # Validate that all required fields are present and not empty
    for field in required_fields:
        if not input_request.get(field):
            log_frame_error(
                logger, message=f"Validation failed: Missing or empty '{field}' field"
            )
            return build_error_response(
                input_request,
                HTTPStatus.BAD_REQUEST.value,
                StatusMessage.INVALID_REQUEST.value,
                f"Validation failed: Missing or empty '{field}' field",
            )

    # Validate document type against allowed types from configuration
    doc_type = input_request["document_type"].strip().upper()
    if doc_type not in ALLOWED_DOCUMENT_TYPES:
        log_frame_error(
            logger, message=f"Validation failed: Invalid document_type '{doc_type}'"
        )
        return build_error_response(
            input_request,
            HTTPStatus.BAD_REQUEST.value,
            StatusMessage.INVALID_DOCUMENT_TYPE.value,
            f"Validation failed: Invalid document_type '{doc_type}'",
        )

    # Validate that the proof document URL points to a PDF file
    proof_url = input_request["proof_document_url"]
    if not urlparse(proof_url).path.lower().endswith(".pdf"):
        log_frame_error(
            logger,
            message=f"Validation failed: proof_document_url '{proof_url}' does not end with .pdf",
        )
        return build_error_response(
            input_request,
            HTTPStatus.UNSUPPORTED_MEDIA_TYPE.value,
            StatusMessage.UNSUPPORTED_MEDIA_TYPE.value,
            f"Validation failed: proof_document_url '{proof_url}' does not end with .pdf",
        )

    # Ensure that the payload contains at least one non-empty value
    # This prevents processing requests with completely empty payloads
    if all(is_empty(value) for value in payload.values()):
        log_frame_error(
            logger, message="Validation failed: All payload parameters are empty"
        )
        return build_error_response(
            input_request,
            HTTPStatus.BAD_REQUEST.value,
            StatusMessage.INVALID_REQUEST.value,
            "Validation failed: All payload parameters are empty",
        )

    # All validations passed successfully
    log_frame_info(logger, message="Event payload validated successfully")
    return {
        "is_valid": True,
        "error": None,
        "request_id": input_request.get("request_id"),
    }
