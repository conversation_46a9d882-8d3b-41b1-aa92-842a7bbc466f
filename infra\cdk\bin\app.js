#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = __importStar(require("aws-cdk-lib"));
const stack_1 = require("../lib/stack");
const app = new cdk.App();
// Get environment from context or environment variable
const deploymentEnv = app.node.tryGetContext('env') || process.env.DEPLOYMENT_ENVIRONMENT || 'development';
// Define account configurations
const accountConfigs = {
    development: {
        accountId: '************',
        region: 'ap-southeast-1'
    },
    staging: {
        accountId: '************',
        region: 'ap-southeast-1'
    },
    production: {
        accountId: '************',
        region: 'ap-southeast-1'
    }
};
// Select the appropriate configuration
const config = accountConfigs[deploymentEnv] || accountConfigs.staging;
// Create stack name based on environment
const getStackName = (env) => {
    switch (env) {
        case 'development':
            return 'OcrVerificationDevelopmentStack';
        case 'staging':
            return 'OcrVerificationStagingStack';
        case 'production':
            return 'OcrVerificationProductionStack';
        default:
            return 'OcrVerificationStagingStack'; // Default fallback
    }
};
// Create the stack
new stack_1.OcrVerificationStack(app, getStackName(deploymentEnv), {
    env: {
        account: config.accountId,
        region: config.region
    },
    envName: deploymentEnv // Pass the environment to the stack using the renamed property
});
// Add tags to all resources in the app
cdk.Tags.of(app).add('Application', 'OcrVerification');
cdk.Tags.of(app).add('Environment', deploymentEnv);
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************