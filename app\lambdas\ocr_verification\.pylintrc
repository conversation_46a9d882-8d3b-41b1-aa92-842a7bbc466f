[MASTER]
# Add any Python module names here (strings) to be ignored by pylint
ignore=CVS
persistent=yes
load-plugins=

[MESSAGES CONTROL]
# Disable specific warnings
disable=C0114,C0115,C0116,C0103,R0903,R0913,W0621,W0703

[REPORTS]
# Set the output format
output-format=text

# Include a brief explanation of each error
msg-template={path}:{line}: [{msg_id}({symbol}), {obj}] {msg}

# Include the symbol name of the messages inside the output
symbols=yes

[BASIC]
# Regular expression which should only match function or class names
good-names=i,j,k,ex,Run,_,id

# Regular expression which should only match correct variable names
good-names-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct function names
good-names-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct class names
good-names-rgx=[A-Z_][a-zA-Z0-9_]+$

# Regular expression which should only match correct module names
good-names-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# Regular expression which should only match correct constant names
good-names-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$

# Regular expression which should only match correct attribute names
good-names-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct argument names
good-names-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match correct variable names
good-names-rgx=[a-z_][a-z0-9_]{2,30}$

[FORMAT]
# Maximum number of characters on a single line
max-line-length=120

# Maximum number of lines in a module
max-module-lines=1000

# String used as indentation unit
indent-string='    '

[SIMILARITIES]
# Minimum lines number of a similarity
min-similarity-lines=4

# Ignore imports when computing similarities
ignore-imports=yes

[MISCELLANEOUS]
# List of note tags to take into consideration
notes= 