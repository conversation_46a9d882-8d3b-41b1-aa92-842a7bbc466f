#!/usr/bin/env python3
"""
Production-ready dual LLM processor for OCR verification.
Processes documents through both Bedrock and Gemini LLMs with identical workflows.
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple

# Set test environment to avoid SSM issues
os.environ['ENV'] = 'test'

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.services.process import process_text_pdf, process_scanned_pdf
from core.llm.llm_factory import LlmFactory
from core.utils.utils import load_pdf_with_text
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_warning, log_frame_debug
from core.enums.enums import ResponseStatus

# Initialize logger
logger = get_logger(__name__)


class DualLLMProcessor:
    """
    Production-ready processor that handles PDF documents through both Bedrock and Gemini LLMs.
    Ensures identical processing workflows and validates consistency between providers.
    """
    
    def __init__(self):
        """Initialize the dual LLM processor."""
        self.bedrock_provider = None
        self.gemini_provider = None
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize both LLM providers."""
        try:
            # Clear any cached providers to ensure fresh instances
            LlmFactory.clear_cache()
            
            # Initialize Bedrock provider
            from core.llm.bedrock import BedrockLlm
            self.bedrock_provider = BedrockLlm
            log_frame_info(logger, "✅ Bedrock LLM provider initialized")
            
            # Initialize Gemini provider
            from core.llm.gemini import GeminiLlm
            self.gemini_provider = GeminiLlm
            log_frame_info(logger, "✅ Gemini LLM provider initialized")
            
        except Exception as e:
            log_frame_error(logger, f"Failed to initialize LLM providers: {str(e)}")
            raise
    

    
    def process_with_provider(self, pdf_bytes: bytes, claim_payload: Dict[str, Any], 
                            provider_name: str, provider_class) -> Dict[str, Any]:
        """
        Process PDF with a specific LLM provider.
        
        Args:
            pdf_bytes: PDF document as bytes
            claim_payload: Claims payload for processing
            provider_name: Name of the provider (for logging)
            provider_class: LLM provider class
            
        Returns:
            Processing results
        """
        log_frame_info(logger, f"🚀 Processing with {provider_name} LLM...")
        
        try:
            start_time = time.time()
            
            # Temporarily override the LLM factory to use specific provider
            original_provider = LlmFactory._provider_instances.get(provider_name.lower())
            LlmFactory._provider_instances[provider_name.lower()] = provider_class
            
            # Set environment variable to ensure correct provider selection
            original_env = os.environ.get('LLM_PROVIDER')
            os.environ['LLM_PROVIDER'] = provider_name.lower()
            
            try:
                # Check if PDF has extractable text
                page_contents = load_pdf_with_text(pdf_bytes)
                
                if page_contents:
                    log_frame_info(logger, f"📄 Processing as text-based PDF with {provider_name}")
                    result = process_text_pdf(claim_payload, page_contents)
                else:
                    log_frame_info(logger, f"🖼️ Processing as scanned PDF with {provider_name}")
                    # Generate a unique request ID for this processing
                    request_id = f"dual_test_{provider_name.lower()}_{int(time.time())}"
                    result = process_scanned_pdf(request_id, pdf_bytes, claim_payload, retry=False)
                
                processing_time = time.time() - start_time
                result["processing_time"] = processing_time
                result["provider"] = provider_name
                
                log_frame_info(logger, f"✅ {provider_name} processing completed in {processing_time:.2f}s")
                return result
                
            finally:
                # Restore original environment and provider cache
                if original_env:
                    os.environ['LLM_PROVIDER'] = original_env
                elif 'LLM_PROVIDER' in os.environ:
                    del os.environ['LLM_PROVIDER']
                
                if original_provider:
                    LlmFactory._provider_instances[provider_name.lower()] = original_provider
                elif provider_name.lower() in LlmFactory._provider_instances:
                    del LlmFactory._provider_instances[provider_name.lower()]
                    
        except Exception as e:
            log_frame_error(logger, f"Error processing with {provider_name}: {str(e)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"{provider_name} processing failed: {str(e)}",
                "provider": provider_name,
                "processing_time": time.time() - start_time if 'start_time' in locals() else 0
            }
    
    def compare_results(self, bedrock_result: Dict[str, Any],
                       gemini_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare results from both LLM providers to ensure consistency with enhanced validation.

        Args:
            bedrock_result: Results from Bedrock processing
            gemini_result: Results from Gemini processing

        Returns:
            Comprehensive comparison analysis with validation details
        """
        log_frame_info(logger, "🔍 Comparing results from both LLM providers...")

        comparison = {
            "status_match": bedrock_result.get("status") == gemini_result.get("status"),
            "bedrock_status": bedrock_result.get("status"),
            "gemini_status": gemini_result.get("status"),
            "differences": [],
            "similarities": [],
            "overall_consistency": True,
            "validation_details": {
                "bedrock_valid": self._validate_result_structure(bedrock_result, "Bedrock"),
                "gemini_valid": self._validate_result_structure(gemini_result, "Gemini"),
                "data_integrity_check": True,
                "critical_fields_match": True
            }
        }
        
        # Check if statuses match - if not, mark as inconsistent
        if not comparison["status_match"]:
            comparison["overall_consistency"] = False
            comparison["validation_details"]["critical_fields_match"] = False
            log_frame_warning(logger, f"Status mismatch: Bedrock={comparison['bedrock_status']}, Gemini={comparison['gemini_status']}")

        # Enhanced comparison for successful results
        if (bedrock_result.get("status") == ResponseStatus.SUCCESS.value and
            gemini_result.get("status") == ResponseStatus.SUCCESS.value):

            # Extract claims data with fallback handling
            bedrock_claims = self._extract_claims_data(bedrock_result, "Bedrock")
            gemini_claims = self._extract_claims_data(gemini_result, "Gemini")

            # Perform detailed field comparison
            field_comparison = self._compare_extracted_fields(bedrock_claims, gemini_claims)
            comparison["differences"].extend(field_comparison["differences"])
            comparison["similarities"].extend(field_comparison["similarities"])

            # Update consistency based on critical field matches
            if field_comparison["critical_mismatches"] > 0:
                comparison["overall_consistency"] = False
                comparison["validation_details"]["critical_fields_match"] = False

            # Add statistical analysis
            comparison["validation_details"]["field_statistics"] = {
                "total_fields": field_comparison["total_fields"],
                "matching_fields": field_comparison["matching_fields"],
                "critical_mismatches": field_comparison["critical_mismatches"],
                "match_percentage": field_comparison["match_percentage"]
            }
            
            # Compare final verification status
            bedrock_final_status = bedrock_result.get("status_message", "")
            gemini_final_status = gemini_result.get("status_message", "")
            
            if bedrock_final_status != gemini_final_status:
                comparison["differences"].append({
                    "field": "final_verification_status",
                    "bedrock_value": bedrock_final_status,
                    "gemini_value": gemini_final_status,
                    "match": False
                })
                comparison["overall_consistency"] = False
        
        # Log comparison results
        if comparison["overall_consistency"]:
            log_frame_info(logger, "✅ Both LLM providers produced consistent results")
        else:
            log_frame_warning(logger, f"⚠️ Found {len(comparison['differences'])} differences between providers")
            for diff in comparison["differences"]:
                log_frame_warning(logger, f"  - {diff['field']}: Bedrock='{diff['bedrock_value']}', Gemini='{diff['gemini_value']}'")
        
        return comparison

    def _validate_result_structure(self, result: Dict[str, Any], provider_name: str) -> bool:
        """
        Validate the structure of LLM processing results.

        Args:
            result: Processing result to validate
            provider_name: Name of the LLM provider

        Returns:
            bool: True if structure is valid
        """
        try:
            required_fields = ["status"]

            # Check required fields
            for field in required_fields:
                if field not in result:
                    log_frame_error(logger, f"{provider_name} result missing required field: {field}")
                    return False

            # Validate status value
            valid_statuses = [ResponseStatus.SUCCESS.value, ResponseStatus.FAIL.value]
            if result.get("status") not in valid_statuses:
                log_frame_error(logger, f"{provider_name} has invalid status: {result.get('status')}")
                return False

            # If successful, validate extracted claims structure
            if result.get("status") == ResponseStatus.SUCCESS.value:
                extracted_claims = result.get("extracted_claims")
                if extracted_claims is not None and not isinstance(extracted_claims, dict):
                    log_frame_error(logger, f"{provider_name} extracted_claims is not a dictionary")
                    return False

            log_frame_debug(logger, f"{provider_name} result structure validation passed")
            return True

        except Exception as e:
            log_frame_error(logger, f"Error validating {provider_name} result structure: {e}")
            return False

    def _extract_claims_data(self, result: Dict[str, Any], provider_name: str) -> Dict[str, Any]:
        """
        Extract claims data from processing result with error handling.

        Args:
            result: Processing result
            provider_name: Name of the LLM provider

        Returns:
            dict: Extracted claims data
        """
        try:
            claims = result.get("extracted_claims", {})
            if not isinstance(claims, dict):
                log_frame_error(logger, f"{provider_name} extracted_claims is not a dictionary: {type(claims)}")
                return {}

            log_frame_debug(logger, f"Extracted {len(claims)} claims from {provider_name}")
            return claims

        except Exception as e:
            log_frame_error(logger, f"Error extracting claims from {provider_name}: {e}")
            return {}

    def _compare_extracted_fields(self, bedrock_claims: Dict[str, Any],
                                 gemini_claims: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform detailed comparison of extracted fields between providers.

        Args:
            bedrock_claims: Claims extracted by Bedrock
            gemini_claims: Claims extracted by Gemini

        Returns:
            dict: Detailed comparison results
        """
        try:
            # Define critical fields that must match
            critical_fields = {
                "policy_number", "claim_number", "insured_name",
                "incident_date", "claim_amount", "policy_holder"
            }

            all_fields = set(bedrock_claims.keys()) | set(gemini_claims.keys())
            differences = []
            similarities = []
            critical_mismatches = 0
            matching_fields = 0

            for field in all_fields:
                bedrock_value = bedrock_claims.get(field, "NOT_FOUND")
                gemini_value = gemini_claims.get(field, "NOT_FOUND")

                # Normalize values for comparison
                bedrock_normalized = self._normalize_field_value(bedrock_value)
                gemini_normalized = self._normalize_field_value(gemini_value)

                if bedrock_normalized == gemini_normalized:
                    similarities.append({
                        "field": field,
                        "value": bedrock_value,
                        "match": True,
                        "is_critical": field in critical_fields
                    })
                    matching_fields += 1
                else:
                    is_critical = field in critical_fields
                    if is_critical:
                        critical_mismatches += 1

                    differences.append({
                        "field": field,
                        "bedrock_value": bedrock_value,
                        "gemini_value": gemini_value,
                        "bedrock_normalized": bedrock_normalized,
                        "gemini_normalized": gemini_normalized,
                        "match": False,
                        "is_critical": is_critical,
                        "similarity_score": self._calculate_similarity(bedrock_normalized, gemini_normalized)
                    })

            total_fields = len(all_fields)
            match_percentage = (matching_fields / total_fields * 100) if total_fields > 0 else 0

            return {
                "differences": differences,
                "similarities": similarities,
                "total_fields": total_fields,
                "matching_fields": matching_fields,
                "critical_mismatches": critical_mismatches,
                "match_percentage": match_percentage
            }

        except Exception as e:
            log_frame_error(logger, f"Error comparing extracted fields: {e}")
            return {
                "differences": [],
                "similarities": [],
                "total_fields": 0,
                "matching_fields": 0,
                "critical_mismatches": 0,
                "match_percentage": 0
            }

    def _normalize_field_value(self, value: Any) -> str:
        """
        Normalize field values for comparison.

        Args:
            value: Field value to normalize

        Returns:
            str: Normalized value
        """
        if value is None or value == "NOT_FOUND":
            return ""

        # Convert to string and normalize
        str_value = str(value).strip().lower()

        # Remove extra whitespace
        str_value = ' '.join(str_value.split())

        # Remove common punctuation for comparison
        import re
        str_value = re.sub(r'[^\w\s]', '', str_value)

        return str_value

    def _calculate_similarity(self, value1: str, value2: str) -> float:
        """
        Calculate similarity score between two string values.

        Args:
            value1: First value
            value2: Second value

        Returns:
            float: Similarity score between 0 and 1
        """
        try:
            if not value1 and not value2:
                return 1.0
            if not value1 or not value2:
                return 0.0

            # Simple character-based similarity
            max_len = max(len(value1), len(value2))
            if max_len == 0:
                return 1.0

            # Count matching characters
            matches = sum(1 for a, b in zip(value1, value2) if a == b)
            return matches / max_len

        except Exception:
            return 0.0

    def process_pdf_dual_llm(self, pdf_path: str, claim_payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main method to process PDF through both LLM providers with OCR quality assessment.

        Args:
            pdf_path: Path to the PDF file
            claim_payload: Claims payload for processing

        Returns:
            Complete processing results including comparison
        """
        log_frame_info(logger, f"🚀 Starting dual LLM processing for: {pdf_path}")

        try:
            # Load PDF file
            with open(pdf_path, 'rb') as f:
                pdf_bytes = f.read()

            log_frame_info(logger, f"📄 Loaded PDF: {len(pdf_bytes)} bytes")

            # Process with both LLM providers
            log_frame_info(logger, "🔄 Processing with both LLM providers...")

            bedrock_result = self.process_with_provider(
                pdf_bytes, claim_payload, "Bedrock", self.bedrock_provider
            )

            gemini_result = self.process_with_provider(
                pdf_bytes, claim_payload, "Gemini", self.gemini_provider
            )

            # Step 3: Compare results
            comparison = self.compare_results(bedrock_result, gemini_result)

            # Step 4: Generate final report
            final_result = {
                "status": ResponseStatus.SUCCESS.value,
                "message": "Dual LLM processing completed",
                "bedrock_result": bedrock_result,
                "gemini_result": gemini_result,
                "comparison": comparison,
                "processing_summary": {
                    "bedrock_success": bedrock_result.get("status") == ResponseStatus.SUCCESS.value,
                    "gemini_success": gemini_result.get("status") == ResponseStatus.SUCCESS.value,
                    "results_consistent": comparison["overall_consistency"],
                    "bedrock_time": bedrock_result.get("processing_time", 0),
                    "gemini_time": gemini_result.get("processing_time", 0)
                }
            }

            # Log final summary
            log_frame_info(logger, "📊 DUAL LLM PROCESSING SUMMARY:")
            log_frame_info(logger, f"  🏗️ Bedrock Success: {final_result['processing_summary']['bedrock_success']}")
            log_frame_info(logger, f"  🤖 Gemini Success: {final_result['processing_summary']['gemini_success']}")
            log_frame_info(logger, f"  🔍 Results Consistent: {final_result['processing_summary']['results_consistent']}")
            log_frame_info(logger, f"  ⏱️ Bedrock Time: {final_result['processing_summary']['bedrock_time']:.2f}s")
            log_frame_info(logger, f"  ⏱️ Gemini Time: {final_result['processing_summary']['gemini_time']:.2f}s")

            return final_result

        except Exception as e:
            log_frame_error(logger, f"Dual LLM processing failed: {str(e)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Dual LLM processing failed: {str(e)}"
            }


def main():
    """Main function to test the dual LLM processor with the test PDF."""
    print("🚀 Starting Production Dual LLM OCR Verification Test")
    print("=" * 60)

    # Test configuration
    pdf_path = "tenancy-contract-01-bad.pdf"

    # Sample claim payload for testing (matching expected format)
    claim_payload = {
        "payload": {
            "tenant_name": "John Smith",
            "property_address": "123 Main Street",
            "monthly_rent": "2500",
            "lease_start_date": "2024-01-01",
            "lease_end_date": "2024-12-31"
        }
    }

    try:
        # Initialize processor
        processor = DualLLMProcessor()

        # Process PDF with both LLMs
        result = processor.process_pdf_dual_llm(pdf_path, claim_payload)

        # Display results
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)

        if result["status"] == ResponseStatus.SUCCESS.value:
            print("✅ DUAL LLM PROCESSING SUCCESSFUL")

            summary = result["processing_summary"]
            print(f"\n📋 Processing Summary:")
            print(f"  Quality Score: {result['quality_assessment']['confidence_score']:.3f}")
            print(f"  Bedrock Success: {'✅' if summary['bedrock_success'] else '❌'}")
            print(f"  Gemini Success: {'✅' if summary['gemini_success'] else '❌'}")
            print(f"  Results Consistent: {'✅' if summary['results_consistent'] else '❌'}")
            print(f"  Bedrock Time: {summary['bedrock_time']:.2f}s")
            print(f"  Gemini Time: {summary['gemini_time']:.2f}s")

            if not summary['results_consistent']:
                print(f"\n⚠️ Found {len(result['comparison']['differences'])} differences:")
                for diff in result['comparison']['differences']:
                    print(f"  - {diff['field']}: Bedrock='{diff['bedrock_value']}', Gemini='{diff['gemini_value']}'")

            # Save detailed results to file
            with open("dual_llm_results.json", "w") as f:
                json.dump(result, f, indent=2, default=str)
            print(f"\n💾 Detailed results saved to: dual_llm_results.json")

        else:
            print("❌ DUAL LLM PROCESSING FAILED")
            print(f"Error: {result.get('message', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
