import pytest
import os
from unittest.mock import patch, MagicMock


@pytest.fixture(scope="module", autouse=True)
def mock_env():
    """Mock environment variables"""
    with patch.dict(
        os.environ,
        {
            "ENV": "DEV",
            "DEFAULT_REGION": "us-east-1",
        },
    ):
        yield


@pytest.fixture(scope="module")
def mock_config_before_import():
    """Mock config module before llm_factory import"""
    mock_config = MagicMock()
    mock_config.ssm_config = {
        "llm_provider": "gemini",
    }

    with patch("config.config.config", mock_config):
        yield mock_config


@pytest.fixture(scope="module")
def mock_logger_before_import():
    """Mock logger functions before llm_factory import"""
    with (
        patch("core.logger.logger.get_logger") as mock_get_logger,
        patch("core.logger.logger.log_frame_info") as mock_log_info,
        patch("core.logger.logger.log_frame_error") as mock_log_error,
    ):
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger

        yield {
            "get_logger": mock_get_logger,
            "log_info": mock_log_info,
            "log_error": mock_log_error,
            "logger": mock_logger,
        }


@pytest.fixture(scope="module")
def mock_utils_before_import():
    """Mock utils functions before llm_factory import"""
    with patch("core.utils.utils.get_json_value") as mock_get_json_value:
        mock_get_json_value.return_value = None
        yield mock_get_json_value


@pytest.fixture(scope="module")
def llm_factory_module(
    mock_config_before_import,
    mock_logger_before_import,
    mock_utils_before_import,
):
    """Import llm_factory module after mocking"""
    # Clear any existing module from cache
    import sys

    modules_to_clear = [mod for mod in sys.modules.keys() if "llm_factory" in mod]
    for mod in modules_to_clear:
        if mod in sys.modules:
            del sys.modules[mod]

    # Import the llm_factory module
    import core.llm.llm_factory as llm_factory_module

    yield llm_factory_module


def test_llm_factory_get_gemini_provider(llm_factory_module):
    """Test getting Gemini provider from factory"""
    with patch("core.llm.gemini.GeminiLlm") as mock_gemini:
        # Clear cache to ensure fresh provider creation
        llm_factory_module.LlmFactory.clear_cache()
        
        provider = llm_factory_module.LlmFactory.get_llm_provider()
        
        assert provider == mock_gemini


def test_llm_factory_get_bedrock_provider(llm_factory_module):
    """Test getting Bedrock provider from factory"""
    with (
        patch("core.llm.bedrock.BedrockLlm") as mock_bedrock,
        patch.dict(os.environ, {"LLM_PROVIDER": "bedrock"}),
        patch.object(llm_factory_module.config, 'ssm_config', {"llm_provider": None}),
    ):
        # Clear cache to ensure fresh provider creation
        llm_factory_module.LlmFactory.clear_cache()
        
        provider = llm_factory_module.LlmFactory.get_llm_provider()
        
        assert provider == mock_bedrock


def test_llm_factory_invalid_provider_fallback(llm_factory_module):
    """Test that invalid provider falls back to Gemini"""
    with (
        patch("core.llm.gemini.GeminiLlm") as mock_gemini,
        patch.dict(os.environ, {"LLM_PROVIDER": "invalid_provider"}),
    ):
        # Clear cache to ensure fresh provider creation
        llm_factory_module.LlmFactory.clear_cache()
        
        provider = llm_factory_module.LlmFactory.get_llm_provider()
        
        # Should fallback to Gemini
        assert provider == mock_gemini


def test_llm_factory_caching(llm_factory_module):
    """Test that provider instances are cached"""
    with patch("core.llm.gemini.GeminiLlm") as mock_gemini:
        # Clear cache
        llm_factory_module.LlmFactory.clear_cache()
        
        # First call should create instance
        provider1 = llm_factory_module.LlmFactory.get_llm_provider()
        
        # Second call should return cached instance
        provider2 = llm_factory_module.LlmFactory.get_llm_provider()
        
        assert provider1 == provider2


def test_llm_factory_clear_cache(llm_factory_module):
    """Test cache clearing functionality"""
    with patch("core.llm.gemini.GeminiLlm") as mock_gemini:
        # Create cached instance
        provider1 = llm_factory_module.LlmFactory.get_llm_provider()
        
        # Clear cache
        llm_factory_module.LlmFactory.clear_cache()
        
        # Should be empty now
        assert len(llm_factory_module.LlmFactory._provider_instances) == 0


def test_llm_factory_get_available_providers(llm_factory_module):
    """Test getting list of available providers"""
    providers = llm_factory_module.LlmFactory.get_available_providers()
    
    assert "bedrock" in providers
    assert "gemini" in providers
    assert len(providers) == 2


def test_get_llm_convenience_function(llm_factory_module):
    """Test the convenience get_llm function"""
    with patch("core.llm.bedrock.BedrockLlm") as mock_bedrock:
        # Clear cache
        llm_factory_module.LlmFactory.clear_cache()
        
        provider = llm_factory_module.get_llm()
        
        assert provider == mock_bedrock 