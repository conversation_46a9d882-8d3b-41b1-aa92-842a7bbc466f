import * as cdk from 'aws-cdk-lib';
import {Construct} from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as path from 'path';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as lambdaEventSources from 'aws-cdk-lib/aws-lambda-event-sources';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import {OcrVerificationParameters} from './parameters';
import {BasicQualityApiGateway} from './api_gateway'; // Updated import

export interface OcrVerificationStackProps extends cdk.StackProps {
  envName?: string;
}

export class OcrVerificationStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: OcrVerificationStackProps) {
    super(scope, id, props);
    
    // Determine environment - default to 'staging' if not specified
    const stage = props?.envName || process.env.DEPLOYMENT_ENV || 'staging';
    
    // Define resource name suffix based on environment
    const resourceNameSuffix = stage === 'production' ? 'production' : stage === 'development' ? 'development' : 'staging';
    
    // Define environment-specific configurations
    const envConfig = {
      development: {
        accountId: '************',
        region: 'ap-southeast-1',
        tags: { 'Environment': 'development' }
      },
      staging: {
        accountId: '************',
        region: 'ap-southeast-1',
        tags: { 'Environment': 'staging' }
      },
      production: {
        accountId: '************',
        region: 'ap-southeast-1',
        tags: { 'Environment': 'production' }
      }
    };
    
    // Select the appropriate configuration
    const config = envConfig[stage as keyof typeof envConfig] || envConfig.staging;
    
    // Define common tags
    const commonTags = {
      'Tribe': 'core-platform',
      'Team': 'compliance',
      'Service': 'ocr-verification',
      'ManagedBy': 'cdk',
      'Project': 'gen-ai',
      ...config.tags
    };
    
    // Apply tags to stack
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(this).add(key, value);
    });
    
        // Create Poppler layer for PDF to image conversion
    const popplerLayer = new lambda.LayerVersion(this, 'PopplerLayer', {
      layerVersionName: `ocr-poppler-layer-${resourceNameSuffix}`,
      code: lambda.Code.fromAsset(path.join(__dirname, '../../../app/lambdas/layers/ocr-poppler-layer/ocr-poppler-layer.zip')),
      compatibleRuntimes: [lambda.Runtime.PYTHON_3_11],
      description: 'Poppler utilities for PDF to image conversion',
    });
    
    // Import existing IAM role
    const requestProcessingRole = iam.Role.fromRoleArn(
      this,
      'RequestProcessingRole',
      `arn:aws:iam::${config.accountId}:role/ocr-verification-request-processing-lambda-role-${resourceNameSuffix}`,
      { mutable: false }
    );

    // Create IAM policy for Textract permissions
    const textractPolicy = new iam.Policy(this, 'TextractPolicy', {
      policyName: `ocr-verification-textract-policy-${resourceNameSuffix}`,
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'textract:DetectDocumentText',
            'textract:AnalyzeDocument',
            'textract:GetDocumentAnalysis',
            'textract:GetDocumentTextDetection',
            'textract:StartDocumentAnalysis',
            'textract:StartDocumentTextDetection'
          ],
          resources: ['*']
        })
      ]
    });

    // Attach the Textract policy to the existing role
    textractPolicy.attachToRole(requestProcessingRole);
    
    // Create IAM policy for CloudWatch Logs permissions
    const cloudWatchLogsPolicy = new iam.Policy(this, 'CloudWatchLogsPolicy', {
      policyName: `ocr-verification-cloudwatch-logs-policy-${resourceNameSuffix}`,
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'logs:CreateLogGroup',
            'logs:CreateLogStream',
            'logs:PutLogEvents',
            'logs:DescribeLogGroups',
            'logs:DescribeLogStreams'
          ],
          resources: [
            `arn:aws:logs:${config.region}:${config.accountId}:log-group:/aws/lambda/ocr-verification-request-processing-lambda-${resourceNameSuffix}`,
            `arn:aws:logs:${config.region}:${config.accountId}:log-group:/aws/lambda/ocr-verification-request-processing-lambda-${resourceNameSuffix}:*`
          ]
        })
      ]
    });

    // Attach the CloudWatch Logs policy to the existing role
    cloudWatchLogsPolicy.attachToRole(requestProcessingRole);
    
    // Import the request processing queue
    const requestProcessingQueue = sqs.Queue.fromQueueArn(
      this,
      'RequestProcessingQueue',
      `arn:aws:sqs:${config.region}:${config.accountId}:ocr-verification-requests-processing-queue-${resourceNameSuffix}`
    );
    
    // Create CloudWatch Log Group for Lambda function
    const lambdaLogGroup = new logs.LogGroup(this, 'RequestProcessingLambdaLogGroup', {
      logGroupName: `/aws/lambda/ocr-verification-request-processing-lambda-${resourceNameSuffix}`,
      retention: logs.RetentionDays.ONE_MONTH, // Adjust retention as needed
      removalPolicy: cdk.RemovalPolicy.DESTROY // Be careful with this in production
    });
    
    // Create SNS Topic for notifications
    const notificationTopic = new sns.Topic(this, 'NotificationTopic', {
      topicName: `ocr-verification-notifications-${resourceNameSuffix}`,
      displayName: `OCR Verification Notifications - ${stage}`,
      fifo: false
    });

    // Create the OCR Request Processing Lambda with bundled dependencies
    const requestProcessingLambda = new lambda.Function(this, 'RequestProcessingLambda', {
      functionName: `ocr-verification-requests-processing-lambda-${resourceNameSuffix}`,
      runtime: lambda.Runtime.PYTHON_3_11,
      handler: 'app.lambda_handler',
      code: lambda.Code.fromAsset(path.join(__dirname, '../../../app/lambdas/ocr_verification'), {
        bundling: {
          image: lambda.Runtime.PYTHON_3_11.bundlingImage,
          command: [
            'bash', '-c',
            'yum install -y libjpeg-devel zlib-devel && ' +
            'pip install -r requirements.txt -t /asset-output && ' +
            'cp -au . /asset-output'
          ],
        },
      }),
      memorySize: 3540,
      ephemeralStorageSize: cdk.Size.mebibytes(2048),
      timeout: cdk.Duration.seconds(900), // 15 minutes for processing large PDFs
      environment: {
        ENV: resourceNameSuffix, // Pass the actual stage (development/staging/production)
        DEFAULT_REGION: config.region,
        LOG_LEVEL: 'INFO',
        NOTIFICATION_TOPIC_ARN: notificationTopic.topicArn, // Add SNS topic ARN as environment variable
        PATH: '/var/runtime:/usr/local/bin:/usr/bin/:/bin:/opt/bin:/var/lang/bin', // Include poppler binaries path
      },
      role: requestProcessingRole,
      layers: [popplerLayer], // Add poppler layer for PDF conversion
      logGroup: lambdaLogGroup // Explicitly associate the log group
    });
    
    // Apply tags to the Lambda function and log group
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(requestProcessingLambda).add(key, value);
      cdk.Tags.of(lambdaLogGroup).add(key, value);
      cdk.Tags.of(notificationTopic).add(key, value);
    });

    // Grant publish permissions to the Lambda function
    notificationTopic.grantPublish(requestProcessingLambda);
    
    // Add SQS trigger for Request Processing Lambda
    requestProcessingLambda.addEventSource(
      new lambdaEventSources.SqsEventSource(requestProcessingQueue, {
        batchSize: 5, // Process fewer messages at once for more intensive processing
        maxBatchingWindow: cdk.Duration.seconds(10)
      })
    );

    // Create SSM Parameters for the environment
    new OcrVerificationParameters(this, 'OcrVerificationParameters', {
      stage: stage,
      resourceNameSuffix: resourceNameSuffix,
      tags: commonTags,
      accountId: config.accountId,
      region: config.region
    });

    // Create the Basic API Gateway (with SQS integration)
    const apiGateway = new BasicQualityApiGateway(this, 'QualityApiGateway', {
      stage: resourceNameSuffix,
      sqsQueueName: `ocr-verification-requests-processing-queue-${resourceNameSuffix}`
    });

    // Apply tags to the API Gateway
    Object.entries(commonTags).forEach(([key, value]) => {
      cdk.Tags.of(apiGateway).add(key, value);
    });

    // Output the API URL for reference
    new cdk.CfnOutput(this, 'ApiUrl', {
      value: apiGateway.api.url,
      description: 'Quality API Gateway URL'
    });

    // Output some key parameters for reference
    new cdk.CfnOutput(this, 'ParameterPrefix', {
      value: `/ocr-verification/${stage}`,
      description: 'SSM Parameter prefix for this environment'
    });

    // Output layer ARN for reference
    new cdk.CfnOutput(this, 'PopplerLayerArn', {
      value: popplerLayer.layerVersionArn,
      description: 'Poppler Layer ARN for PDF conversion'
    });

    // Output Lambda Log Group for reference
    new cdk.CfnOutput(this, 'LambdaLogGroupName', {
      value: lambdaLogGroup.logGroupName,
      description: 'Lambda CloudWatch Log Group Name'
    });
  }
}