import pytest
import json
import os
from unittest.mock import patch, MagicMock, DEFAULT
from botocore.exceptions import ClientError


# Mock AWS credentials and environment before any imports
@pytest.fixture(scope="module", autouse=True)
def mock_aws_credentials():
    """Mock AWS credentials for boto3"""
    with patch.dict(
        os.environ,
        {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "us-east-1",
            "ENV": "DEV",
            "DEFAULT_REGION": "us-east-1",
        },
    ):
        yield


@pytest.fixture(scope="module")
def mock_config_before_import():
    """Mock config module before bedrock import"""
    mock_config = MagicMock()

    # Mock bedrock inference config JSON string
    bedrock_inference_config = {
        "bedrock_model_name": "test-model",
        "bedrock_max_tokens": "4096",
        "bedrock_temperature": "0",
        "bedrock_top_p": "1",
        "max_attempts": "3",
    }

    # Mock ssm_config
    mock_config.ssm_config = {
        "bedrock_model_region": "us-east-1",
        "bedrock_inference_config": json.dumps(bedrock_inference_config),
    }

    with patch("config.config.config", mock_config):
        yield mock_config


@pytest.fixture(scope="module")
def mock_boto3_before_import():
    """Mock boto3 Session and client before bedrock import"""
    with patch("boto3.Session") as mock_session_class:
        # Create mock session instance
        mock_session = MagicMock()
        mock_session_class.return_value = mock_session

        # Create mock bedrock runtime client
        mock_bedrock_client = MagicMock()
        mock_session.client.return_value = mock_bedrock_client

        yield mock_session_class, mock_session, mock_bedrock_client


@pytest.fixture(scope="module")
def mock_utils_before_import():
    """Mock utils functions before bedrock import"""
    with patch("core.utils.utils.get_json_value") as mock_get_json_value:
        # Configure get_json_value to return appropriate values based on key
        def get_json_value_side_effect(json_str, key):
            if not json_str:
                return None
            try:
                data = json.loads(json_str) if isinstance(json_str, str) else json_str
                return data.get(key)
            except:
                return None

        mock_get_json_value.side_effect = get_json_value_side_effect
        yield mock_get_json_value


@pytest.fixture(scope="module")
def mock_logger_before_import():
    """Mock logger functions before bedrock import"""
    with (
        patch("core.logger.logger.get_logger") as mock_get_logger,
        patch("core.logger.logger.log_frame_info") as mock_log_info,
        patch("core.logger.logger.log_frame_warning") as mock_log_warning,
        patch("core.logger.logger.log_frame_debug") as mock_log_debug,
        patch("core.logger.logger.log_frame_error") as mock_log_error,
    ):
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger

        yield {
            "get_logger": mock_get_logger,
            "log_info": mock_log_info,
            "log_warning": mock_log_warning,
            "log_debug": mock_log_debug,
            "log_error": mock_log_error,
            "logger": mock_logger,
        }


@pytest.fixture(scope="module")
def mock_enums_before_import():
    """Mock enums before bedrock import"""
    with patch("core.enums.enums.ResponseStatus") as mock_response_status:
        # Create mock enum values
        mock_response_status.SUCCESS.value = "SUCCESS"
        mock_response_status.FAIL.value = "FAIL"
        mock_response_status.THROTTLE.value = "THROTTLE"

        yield mock_response_status


# Import bedrock only after all mocking is done
@pytest.fixture(scope="module")
def bedrock_module(
    mock_config_before_import,
    mock_boto3_before_import,
    mock_utils_before_import,
    mock_logger_before_import,
    mock_enums_before_import,
):
    """Import bedrock module after mocking"""
    # Clear any existing module from cache
    import sys

    modules_to_clear = [mod for mod in sys.modules.keys() if "bedrock" in mod]
    for mod in modules_to_clear:
        if mod in sys.modules:
            del sys.modules[mod]

    # Import the bedrock module
    from core.llm.bedrock import (
        BedrockLlm,
        bedrock_runtime,
        bedrock_region,
        bedrock_max_attempts,
    )
    import core.llm.bedrock as bedrock_module

    yield bedrock_module


def test_bedrock_initialization(
    bedrock_module, mock_config_before_import, mock_boto3_before_import
):
    """Test bedrock module initialization"""
    mock_session_class, mock_session, mock_bedrock_client = mock_boto3_before_import

    # Test that bedrock_region is set correctly
    assert bedrock_module.bedrock_region == "us-east-1"

    # Test that bedrock_max_attempts is set correctly
    assert bedrock_module.bedrock_max_attempts == 3

    # Test that boto3.Session was called
    mock_session_class.assert_called()

    # Test that bedrock-runtime client was created
    mock_session.client.assert_called_with(
        "bedrock-runtime", config=bedrock_module.bedrock_config
    )


def test_converse_model_success(
    bedrock_module, mock_boto3_before_import, mock_logger_before_import
):
    """Test successful converse_model call"""
    mock_session_class, mock_session, mock_bedrock_client = mock_boto3_before_import
    mock_loggers = mock_logger_before_import

    # Mock successful bedrock response
    mock_response = {
        "output": {"message": {"content": [{"text": "This is the AI response"}]}},
        "usage": {"inputTokens": 100, "outputTokens": 50, "totalTokens": 150},
        "metrics": {"latencyMs": 1500},
    }

    mock_bedrock_client.converse.return_value = mock_response

    # Test messages
    test_messages = [{"role": "user", "content": [{"text": "Hello, how are you?"}]}]

    # Call the method
    result = bedrock_module.BedrockLlm.converse_model(test_messages)

    # Verify the response
    assert result["status"] == "SUCCESS"
    assert result["result"] == "This is the AI response"
    assert result["llm_usage"]["inputTokens"] == 100
    assert result["llm_usage"]["outputTokens"] == 50
    assert result["llm_usage"]["latencyMs"] == 1500

    # Verify bedrock client was called with correct parameters
    mock_bedrock_client.converse.assert_called_once()
    call_args = mock_bedrock_client.converse.call_args[1]

    assert call_args["modelId"] == "test-model"
    assert call_args["messages"] == test_messages
    assert call_args["inferenceConfig"]["maxTokens"] == 4096
    assert call_args["inferenceConfig"]["temperature"] == 0
    assert call_args["inferenceConfig"]["topP"] == 1

    # Verify logging was called
    mock_loggers["log_info"].assert_called()
    mock_loggers["log_debug"].assert_called()


def test_converse_model_without_usage_metrics(bedrock_module, mock_boto3_before_import):
    """Test converse_model with response that has no usage/metrics"""
    mock_session_class, mock_session, mock_bedrock_client = mock_boto3_before_import

    # Mock response without usage/metrics
    mock_response = {
        "output": {"message": {"content": [{"text": "Response without metrics"}]}}
    }

    mock_bedrock_client.converse.return_value = mock_response

    # Call the method
    result = bedrock_module.BedrockLlm.converse_model([])

    # Verify the response
    assert result["status"] == "SUCCESS"
    assert result["result"] == "Response without metrics"
    assert result["llm_usage"] is None


def test_converse_model_throttling_exception(
    bedrock_module, mock_boto3_before_import, mock_logger_before_import
):
    """Test converse_model with throttling exception"""
    mock_session_class, mock_session, mock_bedrock_client = mock_boto3_before_import
    mock_loggers = mock_logger_before_import

    # Create a proper ThrottlingException that inherits from BaseException
    class ThrottlingException(Exception):
        pass

    # Mock the bedrock runtime module and its exceptions
    with patch.object(bedrock_module, "bedrock_runtime") as mock_runtime:
        mock_runtime.converse.side_effect = ThrottlingException("Request throttled")
        mock_runtime.exceptions.ThrottlingException = ThrottlingException

        # Call the method
        result = bedrock_module.BedrockLlm.converse_model([])

        # Verify the response
        assert result["status"] == "THROTTLE"
        assert (
            result["message"] == "Unable to process the request due to Throttling error"
        )
        assert result["data"] is None

        # Verify error logging was called
        mock_loggers["log_error"].assert_called()


def test_converse_model_general_exception(
    bedrock_module, mock_boto3_before_import, mock_logger_before_import
):
    """Test converse_model with general exception"""
    mock_session_class, mock_session, mock_bedrock_client = mock_boto3_before_import
    mock_loggers = mock_logger_before_import

    # Mock the bedrock_runtime object directly in the module
    with patch.object(bedrock_module, "bedrock_runtime") as mock_runtime:
        # Configure the client to raise a general exception
        mock_runtime.converse.side_effect = Exception("General error occurred")

        # Mock the exceptions attribute to avoid the BaseException error
        mock_runtime.exceptions = MagicMock()
        mock_runtime.exceptions.ThrottlingException = type(
            "ThrottlingException", (Exception,), {}
        )

        # Call the method
        result = bedrock_module.BedrockLlm.converse_model([])

        # Verify the response
        assert result["status"] == "FAIL"
        assert "General error occurred" in result["message"]
        assert result["data"] is None

        # Verify error logging was called
        mock_loggers["log_error"].assert_called()


def test_converse_model_with_default_inference_config(
    bedrock_module, mock_utils_before_import
):
    """Test converse_model with default inference config values"""
    with patch("core.utils.utils.get_json_value") as mock_get_json_value:
        # Configure get_json_value to return None for some values (testing defaults)
        def get_json_value_side_effect(json_str, key):
            if key == "bedrock_model_name":
                return "test-model"
            elif key == "bedrock_max_tokens":
                return None  # Should default to "4096"
            elif key == "bedrock_temperature":
                return None  # Should default to "0"
            elif key == "bedrock_top_p":
                return None  # Should default to "1"
            return None

        mock_get_json_value.side_effect = get_json_value_side_effect

        # Mock bedrock client
        with patch.object(bedrock_module, "bedrock_runtime") as mock_client:
            mock_client.converse.return_value = {
                "output": {"message": {"content": [{"text": "test"}]}},
                "usage": {"inputTokens": 10},
            }

            # Mock the exceptions attribute to avoid the BaseException error
            mock_client.exceptions = MagicMock()
            mock_client.exceptions.ThrottlingException = type(
                "ThrottlingException", (Exception,), {}
            )

            # Call the method
            result = bedrock_module.BedrockLlm.converse_model([])

            # Verify default values were used
            call_args = mock_client.converse.call_args[1]
            assert call_args["inferenceConfig"]["maxTokens"] == 4096
            assert call_args["inferenceConfig"]["temperature"] == 0
            assert call_args["inferenceConfig"]["topP"] == 1


def test_converse_model_empty_messages(bedrock_module, mock_boto3_before_import):
    """Test converse_model with empty messages list"""
    mock_session_class, mock_session, mock_bedrock_client = mock_boto3_before_import

    # Mock successful response
    mock_bedrock_client.converse.return_value = {
        "output": {"message": {"content": [{"text": "Empty message response"}]}}
    }

    # Call with empty messages (default parameter)
    result = bedrock_module.BedrockLlm.converse_model()

    # Verify it still works
    assert result["status"] == "SUCCESS"
    assert result["result"] == "Empty message response"

    # Verify bedrock was called with empty messages
    call_args = mock_bedrock_client.converse.call_args[1]
    assert call_args["messages"] == []


def test_config_object_initialization(bedrock_module, mock_config_before_import):
    """Test that config_obj is properly initialized"""
    # Verify config_obj was set from config.ssm_config
    assert bedrock_module.config_obj == mock_config_before_import.ssm_config


def test_bedrock_config_initialization(bedrock_module):
    """Test bedrock_config is properly initialized"""
    config = bedrock_module.bedrock_config

    # Verify retry configuration
    assert hasattr(config, "retries")
    # Note: The exact structure may vary, so we just check it exists


def test_module_level_variables(bedrock_module):
    """Test module-level variables are set correctly"""
    # Test that all expected module variables exist
    assert hasattr(bedrock_module, "bedrock_region")
    assert hasattr(bedrock_module, "bedrock_max_attempts")
    assert hasattr(bedrock_module, "bedrock_config")
    assert hasattr(bedrock_module, "bedrock_runtime")
    assert hasattr(bedrock_module, "BedrockLlm")

    # Test values
    assert bedrock_module.bedrock_region == "us-east-1"
    assert bedrock_module.bedrock_max_attempts == 3


def test_bedrock_llm_class_exists(bedrock_module):
    """Test that BedrockLlm class exists and has expected methods"""
    BedrockLlm = bedrock_module.BedrockLlm

    # Check class exists
    assert BedrockLlm is not None

    # Check converse_model method exists and is a classmethod
    assert hasattr(BedrockLlm, "converse_model")
    assert callable(BedrockLlm.converse_model)
