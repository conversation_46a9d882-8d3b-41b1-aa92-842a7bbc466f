import pytest
import json
import os
from unittest.mock import patch, MagicMock


# Mock AWS credentials and environment before any imports
@pytest.fixture(scope="module", autouse=True)
def mock_aws_credentials():
    """Mock AWS credentials for boto3"""
    with patch.dict(
        os.environ,
        {
            "AWS_ACCESS_KEY_ID": "testing",
            "AWS_SECRET_ACCESS_KEY": "testing",
            "AWS_SECURITY_TOKEN": "testing",
            "AWS_SESSION_TOKEN": "testing",
            "AWS_DEFAULT_REGION": "ap-southeast-1",
            "ENV": "DEV",
        },
    ):
        yield


@pytest.fixture(scope="module")
def mock_boto3_before_import():
    """Mock boto3 clients before config import"""
    with patch("boto3.client") as mock_boto3_client:
        # Create separate mocks for SSM and S3
        mock_ssm = MagicMock()
        mock_s3 = MagicMock()

        # Configure SSM mock responses
        mock_ssm.get_parameters.return_value = {
            "Parameters": [
                {
                    "Name": "/claims-verification-dev/bedrock/inference/config",
                    "Value": "inference-config",
                },
                {
                    "Name": "/claims-verification-dev/bedrock/maximage/size",
                    "Value": "1024",
                },
                {
                    "Name": "/claims-verification-dev/bedrock/region",
                    "Value": "us-east-1",
                },
                {
                    "Name": "/claims-verification-dev/dynamodb/table",
                    "Value": "claims-table",
                },
                {"Name": "/claims-verification-dev/dynamodb/ttl", "Value": "3600"},
                {
                    "Name": "/claims-verification-dev/queue/notification",
                    "Value": "notification-queue",
                },
                {
                    "Name": "/claims-verification-dev/s3/artifacts",
                    "Value": "test-artifacts-bucket",
                },
                {
                    "Name": "/claims-verification-dev/s3/artifacts/prompts",
                    "Value": "prompts/",
                },
                {
                    "Name": "/claims-verification-dev/s3/tracking",
                    "Value": "tracking-bucket",
                },
            ]
        }

        # Configure S3 mock responses
        mock_s3.get_object.side_effect = [
            {"Body": MagicMock(read=lambda: b"Extract claims from scanned PDF prompt")},
            {"Body": MagicMock(read=lambda: b"Extract claims from text PDF prompt")},
            {"Body": MagicMock(read=lambda: b"Claims verification prompt")},
        ]

        # Return appropriate mock based on service name
        def client_side_effect(service_name, **kwargs):
            if service_name == "ssm":
                return mock_ssm
            elif service_name == "s3":
                return mock_s3
            return MagicMock()

        mock_boto3_client.side_effect = client_side_effect
        yield mock_ssm, mock_s3


@pytest.fixture(scope="module")
def mock_file_operations():
    """Mock file operations before config import"""
    ssm_config_data = {
        "DEV_SSM": {
            "bedrock_inference_config": "/claims-verification-dev/bedrock/inference/config",
            "bedrock_max_image_size": "/claims-verification-dev/bedrock/maximage/size",
            "bedrock_model_region": "/claims-verification-dev/bedrock/region",
            "dynamodb_table_name": "/claims-verification-dev/dynamodb/table",
            "dynamodb_record_ttl": "/claims-verification-dev/dynamodb/ttl",
            "notification_success_queue_url": "/claims-verification-dev/queue/notification",
            "s3_bucket_artifacts": "/claims-verification-dev/s3/artifacts",
            "s3_bucket_artifacts_prompts": "/claims-verification-dev/s3/artifacts/prompts",
            "s3_bucket_tracking": "/claims-verification-dev/s3/tracking",
        }
    }

    prompts_data = {
        "extract_claims_from_scanned_pdf": "extract_claims_from_scanned_pdf.txt",
        "extract_claims_from_text_pdf": "extract_claims_from_text_pdf.txt",
        "claims_verification": "claims_verification.txt",
    }

    with patch("builtins.open") as mock_open:
        # Configure mock_open to return different data based on filename
        def open_side_effect(filename, *args, **kwargs):
            mock_file = MagicMock()
            if "ssm_config.json" in filename:
                mock_file.read.return_value = json.dumps(ssm_config_data)
                mock_file.__enter__.return_value = mock_file
                mock_file.__exit__.return_value = None
            elif "prompts.json" in filename:
                mock_file.read.return_value = json.dumps(prompts_data)
                mock_file.__enter__.return_value = mock_file
                mock_file.__exit__.return_value = None
            return mock_file

        mock_open.side_effect = open_side_effect
        yield mock_open


# Import config only after all mocking is done
@pytest.fixture(scope="module")
def config_module(mock_boto3_before_import, mock_file_operations):
    """Import config module after mocking"""
    with (
        patch("os.path.dirname", return_value="/"),
        patch("os.path.abspath", return_value="/config/config.py"),
        patch("os.path.join", side_effect=lambda *args: "/".join(args)),
    ):
        # Reset singleton before import
        import sys

        if "config.config" in sys.modules:
            del sys.modules["config.config"]

        from config.config import Config

        Config._instance = None

        # Import the module
        import config.config as config_module

        yield config_module


def test_ssm_parameters_fetch(config_module, mock_boto3_before_import):
    """Test SSM parameter fetching"""
    mock_ssm, mock_s3 = mock_boto3_before_import

    # Access the config instance
    config = config_module.config

    # Test that SSM parameters were fetched correctly
    assert config.env == "DEV_SSM"
    assert config.aws_region == "us-east-1"

    # Verify SSM client was called
    mock_ssm.get_parameters.assert_called()

    # Check that parameters were loaded correctly
    assert len(config.parameters) == 9
    assert (
        config.parameters["/claims-verification-dev/s3/artifacts"]
        == "test-artifacts-bucket"
    )
    assert config.parameters["/claims-verification-dev/bedrock/region"] == "us-east-1"

    # Test ssm_config mapping
    assert config.ssm_config["s3_bucket_artifacts"] == "test-artifacts-bucket"
    assert config.ssm_config["bedrock_model_region"] == "us-east-1"
    assert config.ssm_config["dynamodb_table_name"] == "claims-table"


def test_get_ssm_parameters_method(config_module, mock_boto3_before_import):
    """Test get_ssm_parameters method directly"""
    mock_ssm, mock_s3 = mock_boto3_before_import

    config = config_module.config

    # Test the method with new parameters
    test_params = ["/test/param1", "/test/param2"]
    mock_ssm.get_parameters.return_value = {
        "Parameters": [
            {"Name": "/test/param1", "Value": "value1"},
            {"Name": "/test/param2", "Value": "value2"},
        ]
    }

    result = config.get_ssm_parameters(test_params)

    # Verify results
    assert len(result) == 2
    assert result["/test/param1"] == "value1"
    assert result["/test/param2"] == "value2"

    # Verify SSM was called with correct parameters
    mock_ssm.get_parameters.assert_called_with(Names=test_params, WithDecryption=True)


def test_s3_prompts_fetch(config_module, mock_boto3_before_import):
    """Test S3 prompts fetching"""
    mock_ssm, mock_s3 = mock_boto3_before_import

    config = config_module.config

    # Test that prompts were loaded correctly
    assert len(config.prompts) == 3
    assert "extract_claims_from_scanned_pdf" in config.prompts
    assert "extract_claims_from_text_pdf" in config.prompts
    assert "claims_verification" in config.prompts

    # Verify S3 client was called for each prompt file
    assert mock_s3.get_object.call_count == 3

    # Check specific prompt content
    assert (
        config.prompts["extract_claims_from_scanned_pdf"]
        == "Extract claims from scanned PDF prompt"
    )
    assert config.prompts["claims_verification"] == "Claims verification prompt"


def test_get_prompts_from_s3_method(config_module, mock_boto3_before_import):
    """Test get_prompts_from_s3 method directly"""
    mock_ssm, mock_s3 = mock_boto3_before_import

    config = config_module.config

    # Setup new mock responses for direct method test
    mock_s3.get_object.side_effect = [
        {"Body": MagicMock(read=lambda: b"Test prompt 1")},
        {"Body": MagicMock(read=lambda: b"Test prompt 2")},
    ]

    # Test prompt files mapping
    test_prompt_files = {"test_prompt1": "test1.txt", "test_prompt2": "test2.txt"}

    # Temporarily replace prompt_files for test
    original_prompt_files = config.prompt_files
    config.prompt_files = test_prompt_files

    result = config.get_prompts_from_s3("test-bucket", "test-prefix/")

    # Verify results
    assert len(result) == 2
    assert result["test_prompt1"] == "Test prompt 1"
    assert result["test_prompt2"] == "Test prompt 2"

    # Verify S3 calls
    expected_calls = [
        (("test-bucket",), {"Key": "test-prefix/test1.txt"}),
        (("test-bucket",), {"Key": "test-prefix/test2.txt"}),
    ]

    # Restore original prompt_files
    config.prompt_files = original_prompt_files


def test_config_properties(config_module):
    """Test config properties (ssm_client, s3_client)"""
    config = config_module.config

    # Test that properties return clients
    ssm_client = config.ssm_client
    s3_client = config.s3_client

    assert ssm_client is not None
    assert s3_client is not None

    # Test that subsequent calls return same instance (cached)
    assert config.ssm_client is ssm_client
    assert config.s3_client is s3_client


def test_ssm_parameter_error_handling(config_module, mock_boto3_before_import):
    """Test SSM parameter error handling"""
    mock_ssm, mock_s3 = mock_boto3_before_import

    config = config_module.config

    # Configure SSM to return invalid parameters
    mock_ssm.get_parameters.return_value = {
        "Parameters": [],
        "InvalidParameters": ["/invalid/param"],
    }

    # Test that ValueError is raised for invalid parameters
    with pytest.raises(ValueError, match="Invalid SSM parameters"):
        config.get_ssm_parameters(["/invalid/param"])


def test_config_get_method(config_module):
    """Test the new get() method of Config class"""
    config = config_module.config

    # Test getting existing values with default
    assert config.get("s3_bucket_artifacts") == "test-artifacts-bucket"
    assert config.get("bedrock_model_region") == "us-east-1"
    
    # Test getting non-existing value with default
    assert config.get("non_existing_key", "default_value") == "default_value"
    
    # Test getting non-existing value without default
    assert config.get("non_existing_key") is None

def test_prompts_interface(config_module):
    """Test the prompts property dictionary-like interface"""
    config = config_module.config

    # Test get() method
    assert config.prompts.get("extract_claims_from_scanned_pdf") == "Extract claims from scanned PDF prompt"
    assert config.prompts.get("extract_claims_from_text_pdf") == "Extract claims from text PDF prompt"
    assert config.prompts.get("non_existing_prompt", "default") == "default"
    assert config.prompts.get("non_existing_prompt") == ""

    # Test dictionary access
    assert config.prompts["extract_claims_from_scanned_pdf"] == "Extract claims from scanned PDF prompt"
    assert config.prompts["claims_verification"] == "Claims verification prompt"

    # Test dictionary access raises KeyError for non-existing key
    with pytest.raises(KeyError):
        _ = config.prompts["non_existing_prompt"]

def test_s3_fetch_error_handling(config_module, mock_boto3_before_import):
    """Test S3 fetch error handling"""
    from botocore.exceptions import ClientError

    mock_ssm, mock_s3 = mock_boto3_before_import

    config = config_module.config

    # Configure S3 to raise ClientError
    mock_s3.get_object.side_effect = ClientError(
        error_response={"Error": {"Code": "NoSuchKey"}}, operation_name="GetObject"
    )

    # Test that ValueError is raised when S3 fetch fails
    with pytest.raises(ValueError, match="Failed to fetch"):
        config.get_prompts_from_s3("test-bucket", "test-prefix/")
