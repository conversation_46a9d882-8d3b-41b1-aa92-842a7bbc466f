import { Construct } from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
export interface BasicQualityApiGatewayProps {
    stage: string;
    sqsQueueName: string;
}
/**
 * Sets up a basic API Gateway with SQS integration.
 * Exposes POST / that sends messages to existing SQS queue.
 */
export declare class BasicQualityApiGateway extends Construct {
    readonly api: apigateway.RestApi;
    constructor(scope: Construct, id: string, props: BasicQualityApiGatewayProps);
}
