from enum import Enum


class StatusMessage(Enum):
    INVALID_REQUEST = "INVALID_REQUEST"
    INVALID_DOCUMENT_TYPE = "INVALID_DOCUMENT_TYPE"
    UNSUPPORTED_MEDIA_TYPE = "UNSUPPORTED_MEDIA_TYPE"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    INVALID_DOCUMENT = "INVALID_DOCUMENT"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    UNCLEAR_DOCUMENT = "UNCLEAR_DOCUMENT"
    PENDING = "PENDING"


class DocumentType(Enum):
    FORM_F = "FORM-F"
    EJARI = "EJARI"


class ResponseStatus(Enum):
    SUCCESS = "SUCCESS"
    THROTTLE = "THROTTLE"
    FAIL = "FAILED"
    FAIL_VALIDATION = "FAILED_VALIDATION"
    PENDING = "PENDING"
