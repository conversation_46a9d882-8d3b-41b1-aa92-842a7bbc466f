# OCR Verification

This repository implements an OCR verification mechanism using the AWS Nova Pro model. It supports both text-based and scanned PDFs, extracting relevant information from the documents based on specified input fields. The extracted data is then compared against the values provided in the request payload, and a result of either ACCEPTED or REJECTED is returned based on the comparison.

Additionally, the system evaluates the quality of the document using an external OCR service with confidence scores to determine whether it can be processed automatically or requires human intervention. This provides objective, data-driven quality assessment based on actual OCR performance.

## Table of Contents
- [Overview](#overview)
- [Technologies Used](#technologies-used)
- [Environment Setup](#environment-setup)
- [Running the Project Locally](#running-the-project-locally)
- [Testing](#testing)
- [OCR Service Migration](#ocr-service-migration)

## Overview

An OCR verification system that extracts data from text and scanned PDFs, compares it with input values, and returns an ACCEPTED or REJECTED result based on the match.

This project utilizes Nova Pro for processing and generating results, ensuring good outputs.

## Technologies Used

- **Python**
- **Lambda**
- **Nova Pro**
- **External OCR Service** (for OCR and quality assessment)
- **AWS (for cloud storage and processing)**

## Environment Setup

To run this project locally, ensure you have the following environment variables set up in your `.env` file:

```env
ENV="development"
LOG_LEVEL=INFO
DEFAULT_REGION=YOUR_REGION
```

Replace placeholders with your actual credentials and information being required.

## Running the Project Locally

Setup AWS credentials in your local environment.

1. **Create and Activate a Virtual Environment**
   ```bash
      python -m venv venv
      source venv/bin/activate
   ```
2. **Install Dependencies**
   ```bash
      pip install -r requirements.txt
   ```
	
   Install python poppler library in your local machine for PDF processing.

## Testing

### Integration Tests

Run the integration tests to verify OCR functionality:

```bash
# Test OCR API integration
python test_ocr_integration.py

# Test quality assessment
python test_lambda_ocr_quality.py

# Test parallel processing for large PDFs
python test_parallel_ocr.py
```

### PyTest Testing Flow

1. **Create and Activate a Virtual Environment**
   ```bash
      python -m venv venv
      source venv/bin/activate
   ```
2. **Install Dependencies**
   ```bash
      pip install -r requirements.txt
   ```

3. **Set Up Test Environment Variables**
Create a .env.test file in the project root with necessary app configurations:
   ```bash
      ENV=test
      LOG_LEVEL=INFO
      DEFAULT_REGION=us-east-1
   ```

4. **Run the Tests**
   ```bash
      pytest --cov=. --cov-report=term-missing --cov-report=html --cov-config=.coveragerc tests/
   ```

## OCR Service Migration

This project has been migrated from AWS Textract to an external OCR service. The migration includes:

- **Intelligent Processing**: Automatic size detection (>6MB = parallel processing, ≤6MB = single file)
- **Parallel Performance**: Large PDFs processed in parallel for faster results
- **Size Optimization**: Handles Lambda Function URL 6MB limit gracefully
- **Backward Compatibility**: Existing code continues to work without changes

For detailed migration information, see [OCR_MIGRATION_SUMMARY.md](OCR_MIGRATION_SUMMARY.md).

### Key Features

- **Automatic Size Detection**: PDFs >6MB are processed as parallel images
- **Parallel Processing**: Uses ThreadPoolExecutor for optimal performance
- **Image Resizing**: Large images are automatically resized to fit payload limits
- **Quality Assessment**: Comprehensive confidence scoring and quality evaluation
- **Error Handling**: Robust error handling with detailed logging

### Configuration

The system uses AWS SSM Parameter Store for configuration:

- `ocr_service_url`: External OCR service URL
- `ocr_confidence_threshold`: Confidence threshold for quality assessment
- `ocr_pdf_timeout`: Timeout for PDF processing (default: 300s)
- `ocr_image_timeout`: Timeout for image processing (default: 120s)
- `ocr_max_workers`: Maximum concurrent workers for parallel processing (default: 8)
