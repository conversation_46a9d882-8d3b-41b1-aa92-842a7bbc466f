{"name": "ocr-verification-project", "version": "1.0.0", "description": "OCR Verification project with CDK infrastructure", "scripts": {"build": "cd infra/cdk && npm install && npm run build", "deploy": "cd infra/cdk && npm install && cdk deploy --require-approval never", "synth": "cd infra/cdk && npm install && cdk synth", "diff": "cd infra/cdk && npm install && cdk diff", "destroy": "cd infra/cdk && cdk destroy", "test": "echo \"No tests specified\" && exit 0"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/node": "^20.4.5", "aws-cdk": "2.175.1", "aws-cdk-lib": "2.175.1", "constructs": "^10.2.69", "esbuild": "^0.18.17", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "dependencies": {"aws-lambda": "^1.0.7"}}