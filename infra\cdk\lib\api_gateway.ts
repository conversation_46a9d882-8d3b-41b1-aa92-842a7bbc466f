import {Construct} from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cdk from 'aws-cdk-lib';

export interface BasicQualityApiGatewayProps {
  stage: string;
  sqsQueueName: string; // Required - name of existing SQS queue
}

/**
 * Sets up a basic API Gateway with SQS integration.
 * Exposes POST / that sends messages to existing SQS queue.
 */
export class BasicQualityApiGateway extends Construct {
  public readonly api: apigateway.RestApi;

  constructor(scope: Construct, id: string, props: BasicQualityApiGatewayProps) {
    super(scope, id);

    // Get current account ID and region
    const account = cdk.Stack.of(this).account;
    const region = cdk.Stack.of(this).region;

    // Create the API Gateway REST API
    this.api = new apigateway.RestApi(this, 'QualityResponseApi', {
      restApiName: `ocr-verification-api-${props.stage}`,
      deployOptions: {
        stageName: props.stage
      },
      endpointTypes: [apigateway.EndpointType.REGIONAL],
      description: 'OCR Verification API with SQS integration'
    });

    // Create an IAM role for API Gateway to assume when calling SQS
    // Trust policy allows API Gateway to assume this role
    const apiGatewayRole = new iam.Role(this, 'ApiGatewayIntegrationRole', {
      // Avoid setting a fixed roleName to prevent collisions across environments
      // CDK will manage the physical name
      assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
      description: `Role assumed by API Gateway to send messages to SQS for ${props.stage}`,
    });

    // Grant permission to send messages to the specific SQS queue for this stage
    const queueArn = `arn:aws:sqs:${region}:${account}:${props.sqsQueueName}`;
    apiGatewayRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: ['sqs:SendMessage'],
      resources: [queueArn],
    }));

    // Create SQS integration for existing queue
    const sqsIntegration = new apigateway.AwsIntegration({
      service: 'sqs',
      path: `${account}/${props.sqsQueueName}`,
      integrationHttpMethod: 'POST',
      options: {
        credentialsRole: apiGatewayRole,
        requestParameters: {
          'integration.request.header.Content-Type': "'application/x-www-form-urlencoded'"
        },
        requestTemplates: {
          'application/json': 'Action=SendMessage&MessageBody=$util.urlEncode($input.body)'
        },
        integrationResponses: [
          {
            statusCode: '200',
            responseTemplates: {
              'application/json': JSON.stringify({
                message: "Message sent to SQS successfully",
                timestamp: "$context.requestTime",
                requestId: "$context.requestId"
              })
            }
          },
          {
            statusCode: '400',
            selectionPattern: '4\\d{2}',
            responseTemplates: {
              'application/json': JSON.stringify({
                error: "Bad Request",
                message: "Invalid request format"
              })
            }
          },
          {
            statusCode: '500',
            selectionPattern: '5\\d{2}',
            responseTemplates: {
              'application/json': JSON.stringify({
                error: "Internal Server Error",
                message: "Failed to send message to SQS"
              })
            }
          }
        ]
      }
    });

    // Add POST method directly to the root resource (no sub-resources)
    this.api.root.addMethod('POST', sqsIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseModels: {
            'application/json': apigateway.Model.EMPTY_MODEL
          }
        },
        {
          statusCode: '400',
          responseModels: {
            'application/json': apigateway.Model.ERROR_MODEL
          }
        },
        {
          statusCode: '500',
          responseModels: {
            'application/json': apigateway.Model.ERROR_MODEL
          }
        }
      ],
      apiKeyRequired: false
    });
  }
}