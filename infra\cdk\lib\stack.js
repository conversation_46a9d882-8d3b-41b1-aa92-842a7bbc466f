"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.OcrVerificationStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const lambda = __importStar(require("aws-cdk-lib/aws-lambda"));
const path = __importStar(require("path"));
const sqs = __importStar(require("aws-cdk-lib/aws-sqs"));
const sns = __importStar(require("aws-cdk-lib/aws-sns"));
const lambdaEventSources = __importStar(require("aws-cdk-lib/aws-lambda-event-sources"));
const iam = __importStar(require("aws-cdk-lib/aws-iam"));
const logs = __importStar(require("aws-cdk-lib/aws-logs"));
const parameters_1 = require("./parameters");
const api_gateway_1 = require("./api_gateway"); // Updated import
class OcrVerificationStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Determine environment - default to 'staging' if not specified
        const stage = props?.envName || process.env.DEPLOYMENT_ENV || 'staging';
        // Define resource name suffix based on environment
        const resourceNameSuffix = stage === 'production' ? 'production' : stage === 'development' ? 'development' : 'staging';
        // Define environment-specific configurations
        const envConfig = {
            development: {
                accountId: '************',
                region: 'ap-southeast-1',
                tags: { 'Environment': 'development' }
            },
            staging: {
                accountId: '************',
                region: 'ap-southeast-1',
                tags: { 'Environment': 'staging' }
            },
            production: {
                accountId: '************',
                region: 'ap-southeast-1',
                tags: { 'Environment': 'production' }
            }
        };
        // Select the appropriate configuration
        const config = envConfig[stage] || envConfig.staging;
        // Define common tags
        const commonTags = {
            'Tribe': 'core-platform',
            'Team': 'compliance',
            'Service': 'ocr-verification',
            'ManagedBy': 'cdk',
            'Project': 'gen-ai',
            ...config.tags
        };
        // Apply tags to stack
        Object.entries(commonTags).forEach(([key, value]) => {
            cdk.Tags.of(this).add(key, value);
        });
        // Create Poppler layer for PDF to image conversion
        const popplerLayer = new lambda.LayerVersion(this, 'PopplerLayer', {
            layerVersionName: `ocr-poppler-layer-${resourceNameSuffix}`,
            code: lambda.Code.fromAsset(path.join(__dirname, '../../../app/lambdas/layers/ocr-poppler-layer/ocr-poppler-layer.zip')),
            compatibleRuntimes: [lambda.Runtime.PYTHON_3_11],
            description: 'Poppler utilities for PDF to image conversion',
        });
        // Import existing IAM role
        const requestProcessingRole = iam.Role.fromRoleArn(this, 'RequestProcessingRole', `arn:aws:iam::${config.accountId}:role/ocr-verification-request-processing-lambda-role-${resourceNameSuffix}`, { mutable: false });

        // Create IAM policy for Textract permissions
        const textractPolicy = new iam.Policy(this, 'TextractPolicy', {
            policyName: `ocr-verification-textract-policy-${resourceNameSuffix}`,
            statements: [
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: [
                        'textract:DetectDocumentText',
                        'textract:AnalyzeDocument',
                        'textract:GetDocumentAnalysis',
                        'textract:GetDocumentTextDetection',
                        'textract:StartDocumentAnalysis',
                        'textract:StartDocumentTextDetection'
                    ],
                    resources: ['*']
                })
            ]
        });

        // Attach the Textract policy to the existing role
        textractPolicy.attachToRole(requestProcessingRole);
        // Import the request processing queue
        const requestProcessingQueue = sqs.Queue.fromQueueArn(this, 'RequestProcessingQueue', `arn:aws:sqs:${config.region}:${config.accountId}:ocr-verification-requests-processing-queue-${resourceNameSuffix}`);
        // Create CloudWatch Log Group for Lambda function
        const lambdaLogGroup = new logs.LogGroup(this, 'RequestProcessingLambdaLogGroup', {
            logGroupName: `/aws/lambda/ocr-verification-request-processing-lambda-${resourceNameSuffix}`,
            retention: logs.RetentionDays.ONE_MONTH, // Adjust retention as needed
            removalPolicy: cdk.RemovalPolicy.DESTROY // Be careful with this in production
        });
        // Create SNS Topic for notifications
        const notificationTopic = new sns.Topic(this, 'NotificationTopic', {
            topicName: `ocr-verification-notifications-${resourceNameSuffix}`,
            displayName: `OCR Verification Notifications - ${stage}`,
            fifo: false
        });
        // Create the OCR Request Processing Lambda with bundled dependencies
        const requestProcessingLambda = new lambda.Function(this, 'RequestProcessingLambda', {
            functionName: `ocr-verification-requests-processing-lambda-${resourceNameSuffix}`,
            runtime: lambda.Runtime.PYTHON_3_11,
            handler: 'app.lambda_handler',
            code: lambda.Code.fromAsset(path.join(__dirname, '../../../app/lambdas/ocr_verification'), {
                bundling: {
                    image: lambda.Runtime.PYTHON_3_11.bundlingImage,
                    command: [
                        'bash', '-c',
                        'yum install -y libjpeg-devel zlib-devel && ' +
                            'pip install -r requirements.txt -t /asset-output && ' +
                            'cp -au . /asset-output'
                    ],
                },
            }),
            memorySize: 3540,
            ephemeralStorageSize: cdk.Size.mebibytes(2048),
            timeout: cdk.Duration.seconds(900), // 15 minutes for processing large PDFs
            environment: {
                ENV: resourceNameSuffix, // Pass the actual stage (development/staging/production)
                DEFAULT_REGION: config.region,
                LOG_LEVEL: 'INFO',
                NOTIFICATION_TOPIC_ARN: notificationTopic.topicArn, // Add SNS topic ARN as environment variable
                PATH: '/var/runtime:/usr/local/bin:/usr/bin/:/bin:/opt/bin:/var/lang/bin', // Include poppler binaries path
            },
            role: requestProcessingRole,
            layers: [popplerLayer], // Add poppler layer for PDF conversion
            logGroup: lambdaLogGroup // Explicitly associate the log group
        });
        // Apply tags to the Lambda function and log group
        Object.entries(commonTags).forEach(([key, value]) => {
            cdk.Tags.of(requestProcessingLambda).add(key, value);
            cdk.Tags.of(lambdaLogGroup).add(key, value);
            cdk.Tags.of(notificationTopic).add(key, value);
        });
        // Grant publish permissions to the Lambda function
        notificationTopic.grantPublish(requestProcessingLambda);
        // Add SQS trigger for Request Processing Lambda
        requestProcessingLambda.addEventSource(new lambdaEventSources.SqsEventSource(requestProcessingQueue, {
            batchSize: 5, // Process fewer messages at once for more intensive processing
            maxBatchingWindow: cdk.Duration.seconds(10)
        }));
        // Create SSM Parameters for the environment
        new parameters_1.OcrVerificationParameters(this, 'OcrVerificationParameters', {
            stage: stage,
            resourceNameSuffix: resourceNameSuffix,
            tags: commonTags,
            accountId: config.accountId,
            region: config.region
        });
        // Create the Basic API Gateway (with SQS integration)
        const apiGateway = new api_gateway_1.BasicQualityApiGateway(this, 'QualityApiGateway', {
            stage: resourceNameSuffix,
            sqsQueueName: `ocr-verification-requests-processing-queue-${resourceNameSuffix}`
        });
        // Apply tags to the API Gateway
        Object.entries(commonTags).forEach(([key, value]) => {
            cdk.Tags.of(apiGateway).add(key, value);
        });
        // Output the API URL for reference
        new cdk.CfnOutput(this, 'ApiUrl', {
            value: apiGateway.api.url,
            description: 'Quality API Gateway URL'
        });
        // Output some key parameters for reference
        new cdk.CfnOutput(this, 'ParameterPrefix', {
            value: `/ocr-verification/${stage}`,
            description: 'SSM Parameter prefix for this environment'
        });
        // Output layer ARN for reference
        new cdk.CfnOutput(this, 'PopplerLayerArn', {
            value: popplerLayer.layerVersionArn,
            description: 'Poppler Layer ARN for PDF conversion'
        });
        // Output Lambda Log Group for reference
        new cdk.CfnOutput(this, 'LambdaLogGroupName', {
            value: lambdaLogGroup.logGroupName,
            description: 'Lambda CloudWatch Log Group Name'
        });
    }
}
exports.OcrVerificationStack = OcrVerificationStack;
//# sourceMappingURL=data:application/json;base64,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