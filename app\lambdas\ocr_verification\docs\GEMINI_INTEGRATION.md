# Gemini LLM Integration

This document describes the integration of Google's Gemini 2.5 Flash model into the OCR verification system.

## Overview

The system now supports multiple LLM providers through a factory pattern:
- **Gemini** (default)
- **Bedrock** (fallback)

## Configuration

### SSM Parameters

The following SSM parameters need to be configured for each environment:

#### Gemini API Key
- **Development**: `/ocr-verification/development/gemini/api/key`
- **Staging**: `/ocr-verification/staging/gemini/api/key`
- **Production**: `/ocr-verification/production/gemini/api/key`

#### Gemini Inference Configuration
- **Development**: `/ocr-verification/development/gemini/inference/config`
- **Staging**: `/ocr-verification/staging/gemini/inference/config`
- **Production**: `/ocr-verification/production/gemini/inference/config`

**Example inference config JSON:**
```json
{
  "gemini_model_name": "gemini-2.5-flash",
  "gemini_max_tokens": "4096",
  "gemini_temperature": "0.0",
  "gemini_top_p": "0.95",
  "gemini_candidate_count": "1",
  "max_attempts": "2"
}
```

#### LLM Provider Selection
- **Development**: `/ocr-verification/development/llm/provider`
- **Staging**: `/ocr-verification/staging/llm/provider`
- **Production**: `/ocr-verification/production/llm/provider`

**Valid values**: `gemini` (default) or `bedrock`

### Environment Variables

For local development, you can also use environment variables:

```bash
# API Key (fallback order: SSM -> GEMINI_API_KEY -> GEMINI_API_KEY_TEST)
export GEMINI_API_KEY="your-gemini-api-key"
export GEMINI_API_KEY_TEST="your-test-gemini-api-key"  # For testing only

# Provider selection (fallback order: SSM -> LLM_PROVIDER -> default: gemini)
export LLM_PROVIDER="bedrock"
```

## Usage

### Using the Factory Pattern

The system automatically selects the appropriate LLM provider based on configuration:

```python
from core.llm.llm_factory import get_llm

# Get the configured LLM provider
llm = get_llm()

# Use the provider (same interface for all providers)
response = llm.converse_model(messages=messages)
```

### Direct Usage (Not Recommended)

You can also import specific providers directly, but using the factory is recommended:

```python
from core.llm.gemini import GeminiLlm

response = GeminiLlm.converse_model(messages=messages)
```

## Response Format

Both Bedrock and Gemini providers return responses in the same format:

```python
{
    "status": "SUCCESS" | "FAIL" | "THROTTLE",
    "result": "AI response text",  # Only on success
    "llm_usage": {  # Usage metrics
        "inputTokens": 100,
        "outputTokens": 50,
        "totalTokens": 150,
        "latencyMs": 1500
    },
    "message": "Error message",  # Only on failure
    "data": None  # Additional data (usually None)
}
```

## Supported Features

### Gemini 2.5 Flash Capabilities
- ✅ Text generation
- ✅ Image understanding (JPEG format)
- ✅ Multimodal prompting
- ✅ System instructions (via prompt design)
- ✅ Structured output (JSON)
- ✅ Rate limiting handling
- ✅ Retry logic with exponential backoff

### Message Format
The system automatically converts messages to the appropriate format for each provider:

```python
messages = [
    {
        "role": "user",
        "content": [
            {"text": "Please analyze this image"},
            {
                "image": {
                    "format": "jpeg",
                    "source": {"bytes": image_bytes}
                }
            }
        ]
    }
]
```

## Error Handling

### Rate Limiting
Gemini API rate limiting is automatically detected and handled:
- Returns `THROTTLE` status for rate limit errors
- Implements exponential backoff retry logic

### API Key Issues
- Missing API key: System falls back to Bedrock provider
- Invalid API key: Returns `FAIL` status with error message

### Network Issues
- Connection errors: Automatic retry with exponential backoff
- Timeout errors: Returns `FAIL` status

## Dependencies

### New Python Package
The integration adds the `google-genai` package to requirements.txt:

```
google-genai
```

### Import Changes
The main service file has been updated to use the factory pattern:

```python
# OLD
from core.llm.bedrock import BedrockLlm

# NEW
from core.llm.llm_factory import get_llm
```

## Testing

### Unit Tests
- `tests/llm/test_gemini.py` - Tests for Gemini implementation
- `tests/llm/test_llm_factory.py` - Tests for factory pattern

### Local Testing
To test with Gemini locally:

1. Set your API key:
   ```bash
   export GEMINI_API_KEY="your-api-key"
   ```

2. Set the provider:
   ```bash
   export LLM_PROVIDER="gemini"
   ```

3. Run your tests or application

## Migration Guide

### From Bedrock to Gemini

1. **Update SSM Parameters**: Add Gemini configuration parameters
2. **Set Provider**: Update `llm_provider` SSM parameter to `"gemini"`
3. **Deploy**: The factory pattern ensures zero-downtime switching
4. **Verify**: Monitor logs for successful Gemini provider initialization

### Fallback Strategy

The system includes automatic fallback:
- If Gemini provider fails to initialize → Falls back to Bedrock
- If SSM parameters are missing → Uses environment variables
- If environment variables are missing → Logs error but continues with available provider

## Monitoring and Logging

### Log Messages
- Provider selection: `"Using LLM provider: {provider_name}"`
- Initialization: `"Initialized {Provider} LLM provider"`
- API calls: `"Gemini model converse successful"`
- Errors: `"Error invoking Gemini model"`

### Metrics
The Gemini provider tracks the same metrics as Bedrock:
- Input tokens
- Output tokens
- Total tokens
- Latency (ms)

## Best Practices

1. **Use the Factory**: Always use `get_llm()` instead of direct imports
2. **Configure Fallback**: Keep Bedrock as fallback for high availability
3. **Monitor Usage**: Track token usage for cost optimization
4. **Test Thoroughly**: Test both providers in staging before production
5. **Handle Errors**: Implement proper error handling for rate limits

## Troubleshooting

### Common Issues

1. **"No Gemini API key found"**
   - Check SSM parameter `/ocr-verification/{env}/gemini/api/key`
   - Verify environment variable `GEMINI_API_KEY`

2. **Rate limiting errors**
   - Implement request spacing
   - Consider upgrading API quotas

3. **Provider fallback**
   - Check logs for initialization errors
   - Verify all required SSM parameters exist

### Debug Mode
Enable debug logging to see detailed provider selection and API calls:

```python
log_frame_debug(logger, message="Model request payload prepared for Gemini")
``` 