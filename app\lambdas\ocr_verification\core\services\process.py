import json
import re
import time
import math
from http import HTTPStatus
from io import Bytes<PERSON>
from PIL import Image
from config.config import config
from core.utils.utils import (
    convert_pdf_to_images,
    is_valid_value,
    is_empty,
    fetch_images_from_s3,
    store_images_to_s3,
)

from core.constants.constants import PAGES_PER_CHUNK
from core.llm.llm_factory import get_llm
import threading
from queue import Queue
from copy import deepcopy
from core.enums.enums import ResponseStatus, StatusMessage
from core.logger.logger import (
    trace_id_var,
    get_logger,
    log_frame_info,
    log_frame_warning,
    log_frame_error,
    log_frame_debug,
)

from concurrent.futures import ThreadPoolExecutor

# Initialize configuration parameters with error handling and default values
try:
    # Maximum image size allowed by Bedrock API (in MB)
    MAX_IMAGE_SIZE_BYTES = float(config.get("bedrock_max_image_size", 5))
except (ValueError, TypeError):
    # Fallback to default values if configuration loading fails
    MAX_IMAGE_SIZE_BYTES = 5

# Convert MB to bytes for Bedrock's Selected Model API limit
MAX_IMAGE_SIZE_BYTES = MAX_IMAGE_SIZE_BYTES * 1024 * 1024  # MB for bytes

# Initialize logger for the current module
logger = get_logger(__name__)


def chunk_pages(page_contents, pages_per_chunk=PAGES_PER_CHUNK):
    """
    Chunks the PDF contents by pages, with the specified number of pages per chunk.

    Args:
        page_contents (dict): Dictionary with page identifiers as keys and content as values
        pages_per_chunk (int): Number of pages to include in each chunk

    Returns:
        list: List of chunked content strings
    """
    try:
        start_time = time.time()

        # Sort pages by their numerical order (extracted from page_num format like "page_1", "page_2")
        sorted_pages = sorted(
            page_contents.items(), key=lambda x: int(x[0].split("_")[1])
        )
        chunks = []
        current_chunk = []

        # Process each page and group them into chunks
        for i, (page_num, content) in enumerate(sorted_pages):
            # Add page header and content to current chunk
            current_chunk.append(f"--- {page_num} ---\n{content}")

            # Create a new chunk when reaching the specified page limit or at the end
            if (i + 1) % pages_per_chunk == 0 or i == len(sorted_pages) - 1:
                chunks.append("\n\n".join(current_chunk))
                current_chunk = []

        log_frame_info(
            logger,
            message=f"PDF chunked into {len(chunks)} segments of approximately {pages_per_chunk} pages each",
        )
        log_frame_info(
            logger, message=f"Chunking took {time.time() - start_time:.2f} seconds"
        )
        return chunks
    except Exception as e:
        log_frame_error(logger, message=f"Error chunking pages: {str(e)}")
        return []


def _validate_and_parse_json(json_str, llm_usage):
    """
    Validate and parse JSON string with comprehensive error handling.

    Args:
        json_str (str): JSON string to validate and parse
        llm_usage (dict): LLM usage metrics for error responses

    Returns:
        dict: Success response with parsed data or failure response with error details
    """
    try:
        # Pre-validation checks
        if not json_str or not json_str.strip():
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Empty JSON string provided",
                "llm_usage": llm_usage
            }

        # Check for common JSON issues
        json_str = json_str.strip()

        # Validate basic JSON structure
        if not (json_str.startswith('{') and json_str.endswith('}')):
            log_frame_error(logger, f"JSON string doesn't start with {{ or end with }}: {json_str[:100]}...")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Invalid JSON structure - missing braces",
                "llm_usage": llm_usage,
                "debug_info": {"json_preview": json_str[:200]}
            }

        # Attempt to parse JSON
        parsed_data = json.loads(json_str)

        # Validate that we got a dictionary
        if not isinstance(parsed_data, dict):
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Expected JSON object, got {type(parsed_data).__name__}",
                "llm_usage": llm_usage
            }

        log_frame_debug(logger, "JSON validation successful")
        return {
            "status": ResponseStatus.SUCCESS.value,
            "data": parsed_data
        }

    except json.JSONDecodeError as e:
        # Enhanced error reporting for JSON decode errors
        error_context = ""
        if e.pos < len(json_str):
            start_pos = max(0, e.pos - 50)
            end_pos = min(len(json_str), e.pos + 50)
            error_context = json_str[start_pos:end_pos]

        log_frame_error(logger, f"JSON decode error at position {e.pos}: {e.msg}")
        log_frame_error(logger, f"Error context: {error_context}")

        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"JSON decode error: {e.msg}",
            "llm_usage": llm_usage,
            "debug_info": {
                "error_position": e.pos,
                "error_context": error_context,
                "json_length": len(json_str)
            }
        }
    except Exception as e:
        log_frame_error(logger, f"Unexpected error during JSON validation: {e}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"JSON validation error: {str(e)}",
            "llm_usage": llm_usage
        }


def extract_answer_content(text):
    """
    Extract JSON content from LLM response, removing scratchpad and cleaning formatting.

    Args:
        text (str): Raw LLM response text

    Returns:
        str: Cleaned JSON content or original text if extraction fails
    """
    try:
        # Clean up double braces that might interfere with JSON parsing
        cleaned = re.sub(r"{{", "{", text)
        cleaned = re.sub(r"}}", "}", cleaned)

        # Extract content between <json> tags if present
        match = re.search(r"<json>(.*?)</json>", cleaned, re.DOTALL)
        if match:
            json_content = match.group(1).strip()

            # Enhanced JSON cleaning for common formatting issues
            json_content = _clean_json_content(json_content)
            return json_content

        # Remove scratchpad sections and return cleaned content
        cleaned_content = re.sub(
            r"<scratchpad>.*?</scratchpad>", "", cleaned, flags=re.DOTALL
        ).strip()

        # If the content looks like JSON, apply cleaning
        if cleaned_content.strip().startswith('{') and cleaned_content.strip().endswith('}'):
            cleaned_content = _clean_json_content(cleaned_content)

        return cleaned_content
    except Exception as e:
        log_frame_error(
            logger, message=f"Error extracting answer content: {str(e)}", key={"text_preview": text[:200]}
        )
        return text


def _clean_json_content(json_content):
    """
    Clean JSON content to fix common formatting issues that cause parsing errors.

    Args:
        json_content (str): Raw JSON content to clean

    Returns:
        str: Cleaned JSON content
    """
    try:
        # Fix common JSON formatting issues
        # Replace line breaks within string values with spaces
        json_content = re.sub(r'("assessment_summary":\s*"[^"]*)\n([^"]*")', r'\1 \2', json_content)
        json_content = re.sub(r'("assessment_summary":\s*"[^"]*)\r\n([^"]*")', r'\1 \2', json_content)

        # Remove any remaining line breaks within quoted strings (more general pattern)
        json_content = re.sub(r'("[^"]*)\n([^"]*")', r'\1 \2', json_content)
        json_content = re.sub(r'("[^"]*)\r\n([^"]*")', r'\1 \2', json_content)

        # Fix escaped quotes that might be double-escaped
        json_content = re.sub(r'\\\\"', '\\"', json_content)

        # Remove trailing commas before closing braces/brackets
        json_content = re.sub(r',(\s*[}\]])', r'\1', json_content)

        # Fix missing commas between object properties (basic pattern)
        json_content = re.sub(r'"\s*\n\s*"', '",\n"', json_content)

        # Normalize whitespace around colons and commas
        json_content = re.sub(r'\s*:\s*', ': ', json_content)
        json_content = re.sub(r'\s*,\s*', ', ', json_content)

        # Remove any control characters that might break JSON parsing
        json_content = ''.join(char for char in json_content if ord(char) >= 32 or char in '\n\r\t')

        log_frame_debug(logger, f"JSON content cleaned, length: {len(json_content)}")
        return json_content.strip()

    except Exception as e:
        log_frame_error(logger, f"Error cleaning JSON content: {e}")
        return json_content


def create_extraction_prompt(raw_text, business_rules, existing_data=None):
    """
    Creates a prompt for extracting structured information from text using business rules.
    For Gemini with function calling, this creates a simplified prompt since the structure
    is enforced by the function schema.

    Args:
        raw_text (str): The raw text content to extract information from
        business_rules (list): List of business rule fields to extract
        existing_data (dict, optional): Previously extracted data to merge with

    Returns:
        str: Formatted prompt string for LLM
    """
    try:
        # Detect if Gemini is being used
        llm_provider = get_llm()
        is_gemini = llm_provider.__class__.__name__ == "GeminiLlm"

        if is_gemini:
            # For Gemini with function calling, use a simplified prompt
            # The structure is enforced by the function schema
            existing_context = ""
            if existing_data and existing_data.get("business_rules"):
                existing_context = f"\n\nPreviously extracted data from earlier chunks:\n{json.dumps(existing_data, ensure_ascii=False, indent=2)}\n\nUse this information to ensure consistency, but prioritize new information found in the current text."

            prompt = f"""You are an expert in extracting structured information from text, including text extracted from scanned PDF images via OCR.

Extract the following business rule fields from the provided text:
{', '.join(business_rules)}

Core Principles:
- Extract data exactly as it appears without translation
- Only extract actual values, not field labels/headings
- No translation between languages
- No inferring missing values
- No mirroring values across languages
- No extraction of field labels as values
- No units/currency symbols should be retrieved with number fields

{existing_context}

Text to process:
{raw_text}

Use the extract_text_data function to return the extracted business rule values in the required JSON structure."""

            log_frame_info(logger, message="Gemini function calling prompt created successfully")
            return prompt
        else:
            # For Bedrock, use the original prompt-based approach
            # Format business rules as a bulleted list
            parameter_list = "\n- ".join([""] + business_rules)

            # Create JSON field structure for the expected output format
            parameter_fields = ",\n".join(
                [f'      "{rule}": ""' for rule in business_rules]
            )

            # Convert existing data to JSON string for context
            existing_data_json = (
                json.dumps(existing_data, ensure_ascii=False, indent=2)
                if existing_data
                else "{}"
            )

            prompt_template = config.prompts.get("extract_claims_from_text_pdf", "")

            prompt = prompt_template.format(
                parameter_list=parameter_list,
                parameter_fields=parameter_fields,
                existing_data=existing_data_json,
                raw_text=raw_text,
            )
            log_frame_info(logger, message="Bedrock prompt created successfully")
            return prompt
    except Exception as e:
        log_frame_error(logger, message=f"Error creating extraction prompt: {str(e)}")
        raise


def create_property_usage_prompt(existing_data=None):
    """
    Creates a prompt for extracting property_usage information from an image.

    Args:
        existing_data (dict, optional): Previously extracted data for context

    Returns:
        str: Formatted prompt string for property usage extraction
    """
    try:
        prompt = config.prompts.get("property_usage", "")
        log_frame_info(logger, message="Property usage prompt created successfully")
        return prompt
    except Exception as e:
        log_frame_error(
            logger, message=f"Error creating property usage prompt: {str(e)}"
        )
        raise


def initialializing_output_json_structure(business_rules):
    """
    Returns the initial empty JSON structure for extraction results.

    Args:
        business_rules (list): List of business rule fields to initialize

    Returns:
        dict: Initial JSON structure with empty values for each business rule
    """
    try:
        structure = {"business_rules": {rule: "" for rule in business_rules}}
        return structure
    except Exception as e:
        log_frame_error(logger, message=f"Error initializing JSON structure: {str(e)}")
        return {"business_rules": {}}


def merge_extraction_results(current_data, new_data={}):
    """
    Merges new extraction results with current data, preferring non-empty values.
    Only updates fields that are currently empty but have valid new values.

    Args:
        current_data (dict): Existing extraction results
        new_data (dict): New extraction results to merge

    Returns:
        dict: Merged extraction results
    """
    # Return current data if new data is invalid or missing business_rules
    if not new_data or "business_rules" not in new_data:
        return current_data

    for field, value in new_data.get("business_rules", {}).items():
        # Only update if field exists, new value is valid, and current value is empty
        if (
            field in current_data["business_rules"]
            and is_valid_value(value)
            and not is_valid_value(current_data["business_rules"][field])
        ):
            current_data["business_rules"][field] = value

    log_frame_debug(logger, message="Results merged successfully")
    return current_data


def invoke_model(prompt, image_bytes=None, business_rules=None):
    """
    Calls AWS Bedrock LLM to extract information from text and/or images.

    Args:
        prompt (str): The prompt text to send to the model
        image_bytes (bytes, optional): Image data to include in the request
        business_rules (list, optional): Business rule fields for dynamic function schema (Gemini)

    Returns:
        dict: Response containing status, result, and usage information
    """
    try:
        start_time = time.time()
        message_content = [{"text": prompt}]

        # Add image to message content if provided
        if image_bytes:
            message_content.append(
                {"image": {"format": "jpeg", "source": {"bytes": image_bytes}}}
            )
        message_list = [{"role": "user", "content": message_content}]
        llm_provider = get_llm()
        
        # Pass business_rules for dynamic function schema generation (Gemini enforcement)
        if business_rules and hasattr(llm_provider, 'converse_model') and 'business_rules' in llm_provider.converse_model.__code__.co_varnames:
            response = llm_provider.converse_model(messages=message_list, business_rules=business_rules)
        else:
            response = llm_provider.converse_model(messages=message_list)
        log_frame_info(
            logger, message=f"Model took time {time.time() - start_time:.2f} seconds"
        )


        print("Raw: ",response)
        
        if 'result' in response and isinstance(response['result'], str):
            json_str = json.dumps(json.loads(response['result']))

        
        # Check if the response indicates a failure status
        status = response.get("status", "")
        if status and status != ResponseStatus.SUCCESS.value:
            return response

        # Extract usage metrics with defaults
        llm_usage = response.get(
            "llm_usage",
            {"inputTokens": 0, "outputTokens": 0, "totalTokens": 0, "latencyMs": 0},
        )

        # Get the actual content from the response
        content = response.get("result", "")
        if not content:
            log_frame_error(logger, message="LLM response content is empty")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Empty LLM response",
                "llm_usage": llm_usage,
            }

        log_frame_debug(logger, message=f"Raw LLM response content: {content}")

        # Extract and clean JSON content from the response
        # json_str = extract_answer_content(content)
        print("json_str: ",json_str)
        if not json_str:
            log_frame_error(
                logger, message="Failed to extract valid JSON from LLM response"
            )
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Invalid or empty JSON in LLM response",
                "llm_usage": llm_usage,
            }

        try:
            # Enhanced JSON validation with detailed error reporting
            result = _validate_and_parse_json(json_str, llm_usage)
            if result.get("status") == ResponseStatus.FAIL.value:
                return result

            response["result"] = result["data"]
            return response
        except json.JSONDecodeError as err:
            log_frame_error(logger, message=f"JSON decode error: {str(err)}")
            log_frame_error(logger, message=f"Problematic JSON at position {err.pos}: {json_str[max(0, err.pos-100):err.pos+100]}")
            log_frame_error(logger, message=f"Full JSON string length: {len(json_str)}")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"JSON decode error: {str(err)}",
                "llm_usage": llm_usage,
                "debug_info": {
                    "json_length": len(json_str),
                    "error_position": err.pos,
                    "error_context": json_str[max(0, err.pos-50):err.pos+50] if err.pos < len(json_str) else "N/A"
                }
            }
    except Exception as e:
        log_frame_error(logger, message="Error occurred", error=str(e))
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"ERROR: {str(e)}",
            "llm_usage": {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": 0,
            },
        }


def verify_claims(user_claim, claims_to_verify):
    """
    Detect anomalies between user claims and verified data using LLM analysis.
    For Gemini, this still uses prompt-based approach since verification logic is complex.

    Args:
        user_claim (dict): Claims provided by the user
        claims_to_verify (dict): Verified claims data to compare against

    Returns:
        dict: Response containing anomaly detection results and status
    """
    try:
        start_time = time.time()

        # Detect if Gemini is being used
        llm_provider = get_llm()
        is_gemini = llm_provider.__class__.__name__ == "GeminiLlm"

        if is_gemini:
            # For Gemini, use a simplified verification prompt since the complex
            # verification logic with buffers is better handled with prompts
            prompt = f"""You are a highly specialized claim verification assistant. Analyze user claims against validated data and identify discrepancies.

CRITICAL GUIDELINES:
- NUMERIC VALUE VERIFICATION: Treat differences in formatting (commas, decimals, whitespace) as non-substantive if numerical value is same
- TEXTUAL VALUE VERIFICATION: Treat text values as equal if they only differ in case (uppercase vs lowercase)
- BUFFER IMPLEMENTATION: For contract_value, property_price & property_size fields, implement 10% buffer
  - User claim is ALWAYS the baseline reference value
  - Calculate acceptable range: [0.9 × user_claim, 1.1 × user_claim]
  - Verified data must fall within this range

INSTRUCTIONS:
1. Analyze user claims against verify_data
2. For each claim, determine: VERIFIED, ANOMALY, or UNVERIFIABLE
3. Overall status: APPROVED only if all claims are VERIFIED, otherwise REJECTED

USER CLAIMS:
{json.dumps(user_claim, ensure_ascii=False, indent=2)}

VERIFICATION DATA:
{json.dumps(claims_to_verify, ensure_ascii=False, indent=2)}

Return a JSON response with:
- query_summary: Brief summary
- claims_analyzed: Array of claim analysis
- overall_assessment: Summary
- confidence_score: 0.00-1.00
- overall_status: APPROVED or REJECTED

Provide detailed reasoning for each verification decision, especially for numeric fields with buffer calculations."""
        else:
            # For Bedrock, use the original detailed prompt
            prompt_template = config.prompts.get("claims_verification", "")
            prompt = prompt_template.format(
                user_claim=json.dumps(user_claim, ensure_ascii=False, indent=2),
                claims_to_verify=json.dumps(claims_to_verify, ensure_ascii=False, indent=2),
            )

        # Send prompt to LLM for anomaly detection
        result = invoke_model(prompt)
        log_frame_info(
            logger,
            message=f"Anomaly detection took {time.time() - start_time:.2f} seconds",
        )
        return result
    except Exception as e:
        log_frame_error(logger, message=f"Error detecting anomalies: {str(e)}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"Error in anomaly detection: {str(e)}",
        }


def finalize_ocr_verification(
    extracted_claims,
    claims_to_verify,
    total_llm_usage,
    overall_start_time,
    doc_type="PDF",
):
    """
    Finalize the OCR verification process by validating extracted claims and performing anomaly detection.

    Args:
        extracted_claims (dict): Claims extracted from the document
        claims_to_verify (dict): Expected claims to verify against
        total_llm_usage (dict): Accumulated LLM usage statistics
        overall_start_time (float): Start time of the entire process
        doc_type (str): Type of document being processed (default: "PDF")

    Returns:
        dict: Final verification result with status and usage information
    """
    # Check if extracted claims are empty or contain only empty values
    if not extracted_claims or all(
        is_empty(value) for value in extracted_claims.values()
    ):
        return {
            "status": StatusMessage.INVALID_DOCUMENT.value,
            "status_code": HTTPStatus.UNPROCESSABLE_ENTITY.value,
            "llm_usage": total_llm_usage,
        }

    # Perform claims verification to detect anomalies
    claims_result = verify_claims(extracted_claims, claims_to_verify)

    # Return early if claims verification failed
    if claims_result["status"] != ResponseStatus.SUCCESS.value:
        return claims_result

    # Accumulate LLM usage statistics from claims verification
    for key in total_llm_usage:
        total_llm_usage[key] += claims_result["llm_usage"].get(key, 0)

    claims_result["llm_usage"] = total_llm_usage
    overall_processing_time = time.time() - overall_start_time
    log_frame_info(
        logger,
        message=f"Total {doc_type} processing time: {overall_processing_time:.2f} seconds",
    )

    return claims_result


def process_text_pdf(claim_payload, page_contents=None):
    """
    Process a text-based PDF: extract text, chunk by pages, extract information, and detect anomalies.

    Args:
        claim_payload (dict): Contains payload with business rules and claims to verify
        page_contents (dict, optional): Dictionary of page contents from PDF

    Returns:
        dict: Processing result with extracted claims and verification status
    """
    try:
        overall_start_time = time.time()

        # Extract business rules and claims from the payload
        business_rules = list(claim_payload["payload"].keys())
        claims_to_verify = claim_payload["payload"]

        # Split PDF content into manageable chunks
        chunks = chunk_pages(page_contents, PAGES_PER_CHUNK)
        if not chunks:
            log_frame_error(logger, message="Failed to chunk PDF pages")
            return {
                "status": ResponseStatus.FAIL,
                "message": "Chunking issues",
            }

        # Initialize empty data structure for accumulating extraction results
        accumulated_data = initialializing_output_json_structure(business_rules)

        # Initialize LLM usage tracking
        total_llm_usage = {
            "inputTokens": 0,
            "outputTokens": 0,
            "totalTokens": 0,
            "latencyMs": 0,
        }

        # Process each chunk sequentially
        for i, chunk in enumerate(chunks):
            log_frame_info(logger, message=f"Processing chunk {i + 1}/{len(chunks)}")

            # Create extraction prompt for current chunk with context from previous results
            prompt = create_extraction_prompt(chunk, business_rules, accumulated_data)
            result = invoke_model(prompt, business_rules=business_rules)
            status = result.get("status", "")

            # Accumulate LLM usage statistics from current chunk
            llm_usage = result.get("llm_usage", None)
            if llm_usage:
                for key in total_llm_usage:
                    total_llm_usage[key] += llm_usage.get(key, 0)

            # Stop processing if current chunk failed
            if status and status != ResponseStatus.SUCCESS.value:
                log_frame_error(
                    logger,
                    message=f"Chunk processing stopped early at chunk {i + 1} due to status: {status}",
                )
                return {
                    "status": status,
                    "message": f"Processing stopped at chunk {i + 1} due to status: {status}",
                    "llm_usage": total_llm_usage,
                }

            # Merge new extraction results with accumulated data
            accumulated_data = merge_extraction_results(
                accumulated_data, result["result"]
            )

        # Extract final business rules from accumulated data
        extracted_claims = accumulated_data.get("business_rules", {})
        log_frame_info(logger, message=f"extracted_claims: {extracted_claims}")

        # Finalize the verification process with anomaly detection
        return finalize_ocr_verification(
            extracted_claims,
            claims_to_verify,
            total_llm_usage,
            overall_start_time,
            doc_type="Text-based PDF",
        )
    except Exception as e:
        log_frame_error(logger, message=f"Error processing text-based PDF: {str(e)}")
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"ERROR: {str(e)}",
            "llm_usage": total_llm_usage,
        }


def encode_image_to_bytes(image):
    """
    Convert a PIL Image to JPEG bytes format for API transmission.

    Args:
        image (PIL.Image): PIL Image object to convert

    Returns:
        bytes: JPEG encoded image data

    Raises:
        Exception: If image encoding fails
    """
    try:
        buffered = BytesIO()
        # Convert to RGB mode and save as JPEG with high quality and optimization
        image.convert("RGB").save(buffered, format="JPEG", quality=90, optimize=True)
        return buffered.getvalue()
    except Exception as e:
        log_frame_error(logger, message=f"Error encoding image to bytes: {str(e)}")
        raise e


def resize_image_to_fit_bytes(image, max_size_bytes=MAX_IMAGE_SIZE_BYTES):
    """
    Resize image to fit within the maximum size limit while maintaining aspect ratio.
    Uses iterative reduction to find optimal size.

    Args:
        image (PIL.Image): PIL Image object to resize
        max_size_bytes (int): Maximum allowed size in bytes

    Returns:
        PIL.Image: Resized image that fits within size limit

    Raises:
        ValueError: If image cannot be resized without becoming too small
    """
    original_width, original_height = image.size

    current_image = image
    current_size = len(encode_image_to_bytes(current_image))

    # Return original if already within limits
    if current_size <= max_size_bytes:
        return current_image

    # Calculate initial reduction factor based on size ratio
    reduction_factor = (max_size_bytes / current_size) ** 0.5

    # Iteratively reduce image size until it fits
    while current_size > max_size_bytes:
        new_width = int(original_width * reduction_factor)
        new_height = int(original_height * reduction_factor)

        # Prevent image from becoming too small to be useful
        if new_width < 100 or new_height < 100:
            raise ValueError(
                "Image cannot be resized to fit size limit without becoming too small"
            )

        # Resize using high-quality LANCZOS resampling
        current_image = image.resize((new_width, new_height), Image.LANCZOS)
        current_size = len(encode_image_to_bytes(current_image))

        # Adjust reduction factor if still too large
        if current_size > max_size_bytes:
            reduction_factor *= 0.9

    log_frame_info(
        logger,
        message=f"Resized image from {original_width}x{original_height} to {current_image.size[0]}x{current_image.size[1]}",
    )
    return current_image


def optimize_image_for_api(image):
    """
    Optimize an image to fit within a conservative size limit for the Bedrock API.
    Uses multiple strategies: quality reduction, resizing, and format conversion.

    Args:
        image (PIL.Image): PIL Image object to optimize

    Returns:
        tuple: (optimized_image, image_bytes, final_size)
            - optimized_image (PIL.Image): Optimized image object
            - image_bytes (bytes): Encoded image data ready for API
            - final_size (int): Final size in bytes
    """
    try:
        start_time = time.time()
        original_width, original_height = image.size
        log_frame_info(
            logger,
            message=f"Original image dimensions: {original_width}x{original_height}",
        )

        def encode_image(image, format="JPEG", quality=95):
            """
            Helper function to encode image with specified format and quality.

            Returns:
                tuple: (image_bytes, size_in_bytes)
            """
            buffered = BytesIO()
            try:
                image.convert("RGB").save(
                    buffered, format=format, quality=quality, optimize=True
                )
                return buffered.getvalue(), len(buffered.getvalue())
            except Exception as e:
                log_frame_error(
                    logger, message=f"Error encoding image to {format}: {str(e)}"
                )
                return None, 0

        # Get initial image size
        img_bytes, current_size = encode_image(image)
        log_frame_info(
            logger,
            message=f"Original image size: {current_size / (1024 * 1024):.2f} MB",
        )

        # Return if already within limits
        if current_size <= MAX_IMAGE_SIZE_BYTES:
            log_frame_info(logger, message="Image already within size limit")
            return image, img_bytes, current_size

        # Set conservative target size (90% of maximum)
        target_size = MAX_IMAGE_SIZE_BYTES * 0.9
        optimized_image = image
        quality = 95
        max_retries = 4
        attempt = 0

        # Multi-stage optimization process
        while current_size > target_size and attempt < max_retries:
            log_frame_info(
                logger, message=f"Optimization attempt {attempt + 1}/{max_retries}"
            )

            # Stage 1: Reduce JPEG quality if still high
            if quality > 85:
                img_bytes, current_size = encode_image(optimized_image, "JPEG", quality)
                log_frame_info(
                    logger,
                    message=f"Size after JPEG quality {quality}: {current_size / (1024 * 1024):.2f} MB",
                )
                if current_size <= target_size:
                    break
                quality -= 5

            # Stage 2: Resize image dimensions
            reduction_factor = math.sqrt(target_size / current_size) * (
                0.98 if attempt == 0 else 0.95
            )
            new_width = int(original_width * reduction_factor)
            new_height = int(original_height * reduction_factor)

            # Prevent image from becoming too small
            if new_width < 100 or new_height < 100:
                log_frame_warning(
                    logger, message="Image dimensions too small after resize"
                )
                break

            # Apply resize with high-quality resampling
            optimized_image = optimized_image.resize(
                (new_width, new_height), Image.LANCZOS
            )
            log_frame_info(logger, message=f"Resized to {new_width}x{new_height}")

            # Check size after resize
            img_bytes, current_size = encode_image(optimized_image, "JPEG", 90)
            log_frame_info(
                logger,
                message=f"Size after resize: {current_size / (1024 * 1024):.2f} MB",
            )

            # Stage 3: Try PNG encoding as last resort
            if current_size > target_size and attempt == max_retries - 1:
                log_frame_info(logger, message="Switching to PNG encoding as fallback")
                img_bytes, current_size = encode_image(optimized_image, "PNG")
                log_frame_info(
                    logger,
                    message=f"Size after PNG encoding: {current_size / (1024 * 1024):.2f} MB",
                )

            attempt += 1

        # Check if optimization was successful
        if current_size > target_size:
            log_frame_error(
                logger,
                message=f"Failed to reduce image size below {target_size / (1024 * 1024):.2f} MB",
            )
            raise Exception(
                f"Image still too large: {current_size / (1024 * 1024):.2f} MB"
            )

        # Warn if size is close to maximum limit
        if current_size > (MAX_IMAGE_SIZE_BYTES * 0.96):
            log_frame_warning(
                logger,
                message=f"Image size {current_size / (1024 * 1024):.2f} MB is close to 5 MB limit",
            )

        # Log final optimization results
        optimization_time = time.time() - start_time
        log_frame_info(
            logger,
            message=f"Final optimized image size: {current_size / (1024 * 1024):.2f} MB, took {optimization_time:.2f}s",
        )
        return optimized_image, img_bytes, current_size
    except Exception as e:
        log_frame_error(logger, message=f"Error optimizing image: {str(e)}")
        return image, None, 0


def extract_property_usage_from_image(image, accumulated_data):
    """
    Extract property usage information from a single image using AI model processing.

    Args:
        image: PIL Image object to be processed
        accumulated_data: Previously extracted data to build upon

    Returns:
        dict: Response containing status, message, and extracted data
    """
    try:
        # Create a specialized prompt for property usage extraction
        prompt = create_property_usage_prompt(existing_data=accumulated_data)

        orig_buffered = BytesIO()
        image.save(orig_buffered, format="PNG")
        current_size = len(orig_buffered.getvalue())
        log_frame_info(
            logger,
            message=f"Original image size: {current_size / (1024 * 1024):.2f} MB",
        )

        # Optimize image for API processing (compression, format conversion, etc.)
        optimized_image, img_bytes, image_size = optimize_image_for_api(image)
        if img_bytes is None or image_size == 0:
            log_frame_error(logger, message="Failed to optimize image")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Failed to optimize image for API",
            }

        # Check if optimized image exceeds API size limits
        if image_size > MAX_IMAGE_SIZE_BYTES:
            log_frame_error(
                logger,
                message=f"Image too large after optimization: {image_size / (1024 * 1024):.2f} MB",
            )
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Image too large for API: {image_size / (1024 * 1024):.2f} MB > 5 MB limit",
            }

        # Encode the optimized image to bytes format for API transmission
        image_bytes = encode_image_to_bytes(optimized_image)

        # Make API call to extract property usage data and measure latency
        api_call_start_time = time.time()
        response = invoke_model(prompt, image_bytes)
        api_call_time = time.time() - api_call_start_time
        log_frame_info(
            logger,
            message=f"Property usage extracted successfully with latency: {api_call_time:.2f}s",
        )
        return response
    except Exception as e:
        log_frame_error(logger, message=f"Error extracting property usage: {str(e)}")
        return {"status": ResponseStatus.FAIL.value, "message": f"ERROR: {str(e)}"}


def extract_parameters_from_image(image, business_rules, existing_data=None):
    """
    Extract general parameters from a scanned image using AI service.
    For Gemini, uses function calling; for Bedrock, uses prompt-based approach.

    Args:
        image: PIL Image object to be processed
        business_rules: List of parameter keys to extract
        existing_data: Previously extracted data to merge with new results

    Returns:
        dict: Response containing status, message, and extracted parameters
    """
    try:
        orig_buffered = BytesIO()
        image.save(orig_buffered, format="PNG")
        current_size = len(orig_buffered.getvalue())
        log_frame_info(
            logger,
            message=f"Original image size: {current_size / (1024 * 1024):.2f} MB",
        )

        # Detect if Gemini is being used
        llm_provider = get_llm()
        is_gemini = llm_provider.__class__.__name__ == "GeminiLlm"

        if is_gemini:
            # For Gemini with function calling, use a simplified prompt
            existing_context = ""
            if existing_data and existing_data.get("business_rules"):
                existing_context = f"\n\nPreviously extracted data:\n{json.dumps(existing_data, ensure_ascii=False, indent=2)}\n\nUse this information to ensure consistency."

            prompt = f"""You are an expert in extracting structured information from document images.

Extract the following business rule fields from the provided document image:
{', '.join(business_rules)}

Core Principles:
- Extract data exactly as it appears without translation
- Only extract actual values, not field labels/headings
- No translation between languages
- No inferring missing values
- No units/currency symbols with number fields

{existing_context}

Use the extract_document_data function to return the extracted business rule values."""
        else:
            # For Bedrock, use the original prompt-based approach
            # Create numbered list of parameters for the AI prompt
            parameter_list = "\n".join(
                f"{i + 1}. {key}" for i, key in enumerate(business_rules)
            )

            # Format parameter fields as JSON structure for prompt template
            parameter_fields = ",\n".join(
                [f'      "{rule}": ""' for rule in business_rules]
            )

            # Convert existing data to JSON string for context in the prompt
            existing_data_json = (
                json.dumps(existing_data, ensure_ascii=False, indent=2)
                if existing_data
                else "{}"
            )

            prompt_template = config.prompts.get("extract_claims_from_scanned_pdf", "")

            prompt = prompt_template.format(
                parameter_list=parameter_list,
                parameter_fields=parameter_fields,
                existing_data=existing_data_json,
            )

        # Optimize image for API processing
        optimized_image, img_bytes, image_size = optimize_image_for_api(image)
        if img_bytes is None or image_size == 0:
            log_frame_error(logger, message="Failed to optimize image")
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Failed to optimize image for API",
            }

        # Validate image size against API limits
        if image_size > MAX_IMAGE_SIZE_BYTES:
            log_frame_error(
                logger,
                message=f"Image too large after optimization: {image_size / (1024 * 1024):.2f} MB",
            )
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"Image too large for API: {image_size / (1024 * 1024):.2f} MB > 5 MB limit",
            }

        # Encode image to bytes for API transmission
        image_bytes = encode_image_to_bytes(optimized_image)
        log_frame_info(
            logger,
            message=f"Sending image to API - Size: {image_size / (1024 * 1024):.2f} MB",
        )

        # Execute API call and measure response time
        api_call_start_time = time.time()
        response = invoke_model(prompt, image_bytes, business_rules=business_rules)
        api_call_time = time.time() - api_call_start_time
        log_frame_info(
            logger,
            message=f"Parameters extracted successfully with latency: {api_call_time:.2f}s",
        )
        return response
    except Exception as e:
        log_frame_error(
            logger, message=f"Error extracting parameters: {str(e)}", exc_info=True
        )
        return {"status": ResponseStatus.FAIL.value, "message": f"ERROR: {str(e)}"}


def extract_from_images_thread(
    images,
    business_rules,
    accumulated_data,
    results_queue,
    request_id,
    is_property_usage=False,
    stop_flag=None,
):
    if request_id:
        trace_id_var.set(request_id)

    # Initialize thread-specific variables
    thread_results = []
    thread_llm_usage = {
        "inputTokens": 0,
        "outputTokens": 0,
        "totalTokens": 0,
        "latencyMs": 0,
    }

    # Process each image page sequentially
    for page_num, image in images:
        if stop_flag and stop_flag.is_set():
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "Stopped early due to low quality or other thread failure.",
                "llm_usage": thread_llm_usage,
            }

        log_frame_info(
            logger,
            message=f"{'Property usage' if is_property_usage else 'General'} extraction on page {page_num}",
        )
        try:
            if is_property_usage:
                result = extract_property_usage_from_image(image, accumulated_data)
            else:
                result = extract_parameters_from_image(
                    image, business_rules, accumulated_data
                )

            status = result.get("status")
            if status != ResponseStatus.SUCCESS.value:
                if stop_flag:
                    stop_flag.set()
                return {
                    "status": status,
                    "message": f"Error on page {page_num}: {result.get('message', 'Unknown')}",
                    "llm_usage": thread_llm_usage,
                }

            accumulated_data = merge_extraction_results(
                accumulated_data, result.get("result", {})
            )

            llm_usage = result.get("llm_usage", {})
            for key in thread_llm_usage:
                thread_llm_usage[key] += llm_usage.get(key, 0)

            thread_results.append(result)
            image.close()

        except Exception as e:
            log_frame_error(
                logger, message=f"Thread error on page {page_num}: {str(e)}"
            )
            if stop_flag:
                stop_flag.set()
            return {
                "status": ResponseStatus.FAIL.value,
                "message": str(e),
                "llm_usage": thread_llm_usage,
            }

    return {
        "status": ResponseStatus.SUCCESS.value,
        "results": thread_results,
        "accumulated_data": accumulated_data,
        "llm_usage": thread_llm_usage,
    }


def process_scanned_pdf(request_id, pdf_bytes, claim_payload, retry):
    try:
        overall_start_time = time.time()
        total_llm_usage = {
            "inputTokens": 0,
            "outputTokens": 0,
            "totalTokens": 0,
            "latencyMs": 0,
        }

        business_rules = list(claim_payload["payload"].keys())
        claims_to_verify = claim_payload["payload"]

        log_frame_info(logger, message="Converting PDF to images")

        if retry:
            images = fetch_images_from_s3(request_id)
            if (
                isinstance(images, dict)
                and images.get("status") == ResponseStatus.FAIL.value
            ):
                return images
        else:
            # Convert PDF to images for first-time processing
            images = convert_pdf_to_images(pdf_bytes)
            if not images:
                return {
                    "status": ResponseStatus.FAIL.value,
                    "message": "Failed to convert PDF to images",
                    "llm_usage": total_llm_usage,
                }
            # Cache images in S3 for potential retry scenarios
            store_images_to_s3(images, request_id)

        # Validate that we have images to process
        if not images:
            return {
                "status": ResponseStatus.FAIL.value,
                "message": "No images to process",
                "llm_usage": total_llm_usage,
            }

        stop_flag = threading.Event()
        results = {}
        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = {}

            # Submit general extraction
            accumulated_data_general = initialializing_output_json_structure(
                business_rules
            )
            futures["general"] = executor.submit(
                extract_from_images_thread,
                deepcopy(images),
                business_rules,
                accumulated_data_general,
                Queue(),
                request_id,
                False,
                stop_flag,
            )

            # Submit property usage extraction only if needed
            if "property_usage" in business_rules:
                property_usage_rules = ["property_usage"]
                accumulated_data_property = initialializing_output_json_structure(
                    property_usage_rules
                )
                futures["property"] = executor.submit(
                    extract_from_images_thread,
                    deepcopy(images),
                    property_usage_rules,
                    accumulated_data_property,
                    Queue(),
                    request_id,
                    True,
                    stop_flag,
                )

            # Wait for all futures and collect results
            for name, future in futures.items():
                results[name] = future.result()
                if results[name]["status"] != ResponseStatus.SUCCESS.value:
                    stop_flag.set()
                    return results[name]

        # Merge general result
        general_result = results["general"]
        merged_data = general_result["accumulated_data"]

        for key in total_llm_usage:
            total_llm_usage[key] += general_result["llm_usage"].get(key, 0)

        # Merge property usage result if present
        if "property" in results:
            property_result = results["property"]
            property_rules = property_result.get("accumulated_data", {}).get(
                "business_rules", {}
            )
            if "property_usage" in property_rules:
                merged_data.setdefault("business_rules", {})["property_usage"] = (
                    property_rules["property_usage"]
                )
            for key in total_llm_usage:
                total_llm_usage[key] += property_result["llm_usage"].get(key, 0)

        extracted_claims = merged_data.get("business_rules", {})
        log_frame_info(logger, message=f"extracted_claims: {extracted_claims}")

        final_output = finalize_ocr_verification(
            extracted_claims,
            claims_to_verify,
            total_llm_usage,
            overall_start_time,
            doc_type="Scanned PDF",
        )
        return final_output

    except Exception as e:
        log_frame_error(
            logger, message=f"Error processing scanned PDF: {str(e)}", exc_info=True
        )
        return {
            "status": ResponseStatus.FAIL.value,
            "message": f"ERROR: {str(e)}",
            "llm_usage": total_llm_usage
            if "total_llm_usage" in locals()
            else {
                "inputTokens": 0,
                "outputTokens": 0,
                "totalTokens": 0,
                "latencyMs": 0,
            },
        }
