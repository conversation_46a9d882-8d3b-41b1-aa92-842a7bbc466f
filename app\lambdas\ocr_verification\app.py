import json
import time
from http import HTTPStatus
from core.utils.utils import (
    download_pdf,
    load_pdf_with_text,
    publish_to_notification_topic,
    save_request_to_dynamodb,
    calculate_llm_usage_metrics,
    convert_floats_to_decimals,
    extract_anomalies,
    get_record_from_dynamodb,
)
from core.services.process import process_text_pdf, process_scanned_pdf
from core.validations.validation import validate_event
from core.enums.enums import StatusMessage, ResponseStatus
from dotenv import find_dotenv, load_dotenv
import uuid
from core.logger.logger import (
    get_logger,
    trace_id_var,
    log_frame_info,
    log_frame_debug,
    log_frame_error,
)

# Initialize logger
logger = get_logger(__name__)

# Load environment variables from .env file
load_dotenv(find_dotenv())


def handle_processing_response(
    response,
    body,
    request_id,
    execution_start_time,
    ddb_record=None,
    processing_type="",
):
    """Handle the response from PDF processing (both text and image-based)."""

    status = response.get("status", "")
    execution_end_time = time.time()
    total_execution_time = execution_end_time - execution_start_time

    # Create base response structure with common fields
    base_response = {
        "request_id": body["request_id"],
        "request_type": body["request_type"],
    }

    # Handle Bedrock throttling errors - retry scenario
    if status == ResponseStatus.THROTTLE.value:
        error_msg = "Bedrock throttling error occurred: " + str(response)
        log_frame_error(logger, message=error_msg)
        final_response = {
            **base_response,
            "status_code": HTTPStatus.TOO_MANY_REQUESTS.value,
            "status": StatusMessage.PENDING.value,
            "message": response.get("message", error_msg),
        }
        save_request_to_dynamodb(
            request_id, body["request_type"], body, final_response, total_execution_time
        )
        # Raise exception to trigger retry mechanism
        raise Exception(error_msg)

    # Handle general processing failures
    elif status == ResponseStatus.FAIL.value:
        final_response = {
            **base_response,
            "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
            "status": StatusMessage.REJECTED.value,
            "message": response.get("message", "unknown error"),
        }
        save_request_to_dynamodb(
            request_id, body["request_type"], body, final_response, total_execution_time
        )
        publish_to_notification_topic(request_id, final_response)

    # Handle invalid document scenarios (e.g., wrong document type)
    elif status == StatusMessage.INVALID_DOCUMENT.value:
        final_response = {
            **base_response,
            "status_code": HTTPStatus.UNPROCESSABLE_ENTITY.value,
            "status": StatusMessage.INVALID_DOCUMENT.value,
            "message": response.get("Document is invalid"),
        }
        save_request_to_dynamodb(
            request_id, body["request_type"], body, final_response, total_execution_time
        )
        publish_to_notification_topic(request_id, final_response)

    # Handle unclear/poor quality documents
    elif status == StatusMessage.UNCLEAR_DOCUMENT.value:
        prior_llm_usage = ddb_record.get("llm_usage", {}) if ddb_record else {}
        llm_usage = calculate_llm_usage_metrics(response, prior_llm_usage)

        final_response = {
            **base_response,
            "status_code": HTTPStatus.UNPROCESSABLE_ENTITY.value,
            "status": StatusMessage.UNCLEAR_DOCUMENT.value,
            "message": response.get("message", "unknown error"),
            "llm_usage": convert_floats_to_decimals(llm_usage),
            "quality_assessments": response.get("quality_assessments", []),
        }
        save_request_to_dynamodb(
            request_id, body["request_type"], body, final_response, total_execution_time
        )
        publish_to_notification_topic(request_id, final_response)

    # Handle successful processing
    elif status == ResponseStatus.SUCCESS.value:
        # Calculate LLM usage metrics, merging with any prior usage from retries
        prior_llm_usage = ddb_record.get("llm_usage", {}) if ddb_record else {}
        llm_usage = calculate_llm_usage_metrics(response, prior_llm_usage)
        claims_result = response.get("result", {})

        # Determine if the document/claim is approved based on overall status
        is_approved = (
            claims_result.get("overall_status", "") == StatusMessage.APPROVED.value
        )

        final_response = {
            **base_response,
            "status_code": HTTPStatus.OK.value,
            "status": StatusMessage.APPROVED.value
            if is_approved
            else StatusMessage.REJECTED.value,
            "llm_usage": convert_floats_to_decimals(llm_usage),
            "quality_assessments": response.get("quality_assessments", []),
        }

        # Add anomaly detection results only for rejected documents
        if not is_approved:
            final_response["anomaly_detection"] = extract_anomalies(claims_result)

        save_request_to_dynamodb(
            request_id, body["request_type"], body, final_response, total_execution_time
        )
        publish_to_notification_topic(request_id, final_response)
        log_frame_info(
            logger,
            message=f"{processing_type} PDF processing completed with status: {status}",
        )
        return {
            "status_code": HTTPStatus.OK.value,
            "message": "Request Processed Successfully",
        }


def lambda_handler(event, context):
    """Main Lambda handler for processing PDF documents from SQS queue."""

    # Process SQS batch records
    if event:
        batch_item_failures = []
        sqs_batch_response = {}

        # Process each SQS record in the batch
        for record in event["Records"]:
            try:
                execution_start_time = time.time()

                # Parse SQS message body to extract request details
                body = record["body"]
                body = json.loads(body)
                request_id = body.get("request_id", None)

                # Generate new request ID if not provided
                if not request_id:
                    request_id = str(uuid.uuid4())
                    log_frame_debug(
                        logger, message=f"Generated new request_id: {request_id}"
                    )

                # Check if this is a retry by looking up existing DynamoDB record
                ddb_record = get_record_from_dynamodb(request_id)
                retry = (
                    ddb_record
                    and ddb_record.get("status") == StatusMessage.PENDING.value
                )

                # Validate incoming request payload
                validation_result = validate_event(body)

                trace_id_var.set(request_id)
                log_frame_info(
                    logger, message=f"Validation result: {validation_result}"
                )

                # Handle validation failures
                if not validation_result.get("is_valid", False):
                    log_frame_info(
                        logger,
                        message=f"Invalid event, returning early. Status code: {validation_result['status_code']}",
                    )
                    execution_end_time = time.time()
                    total_execution_time = execution_end_time - execution_start_time

                    publish_to_notification_topic(request_id, validation_result)
                    save_request_to_dynamodb(
                        request_id,
                        body.get("request_type", ""),
                        body,
                        validation_result,
                        total_execution_time,
                    )

                    return validation_result

                pdf_path = body["proof_document_url"]
                log_frame_info(
                    logger, message=f"Downloading PDF from path: {pdf_path}"
                )

                # Download PDF file from S3 storage
                try:
                    pdf_bytes = download_pdf(pdf_path)
                except Exception as e:
                    # Handle S3 download failures
                    execution_end_time = time.time()
                    total_execution_time = execution_end_time - execution_start_time
                    failure_result = {
                        "request_id": request_id,
                        "status": StatusMessage.REJECTED.value,
                        "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                        "request_type": body.get("request_type", None),
                        "message": f"Error downloading PDF: {str(e)}",
                    }

                    publish_to_notification_topic(request_id, failure_result)
                    save_request_to_dynamodb(
                        request_id,
                        body.get("request_type", ""),
                        body,
                        failure_result,
                        total_execution_time,
                    )
                    return failure_result

                # Extract text content from PDF for analysis
                page_contents = load_pdf_with_text(pdf_bytes)

                if page_contents:
                    # Process text-based PDFs (documents with extractable text)
                    log_frame_info(
                        logger,
                        message=f"Text extracted successfully from PDF: {pdf_path}",
                    )
                    response = process_text_pdf(body, page_contents)
                    return handle_processing_response(
                        response,
                        body,
                        request_id,
                        execution_start_time,
                        ddb_record,
                        "Text-based",
                    )

                else:
                    # Process scanned PDFs containing images
                    log_frame_info(
                        logger,
                        message="PDF appears to be scanned or has minimal text, proceeding with image-based processing",
                    )
                    response = process_scanned_pdf(request_id, pdf_bytes, body, retry)
                    return handle_processing_response(
                        response,
                        body,
                        request_id,
                        execution_start_time,
                        ddb_record,
                        "Image-based",
                    )

            except Exception as e:
                # Add failed records to batch response for SQS retry
                log_frame_error(
                    logger, message=f"Lambda execution error: {str(e)}", exc_info=True
                )
                batch_item_failures.append({"itemIdentifier": record["messageId"]})

        # Return batch processing results to SQS
        sqs_batch_response["batchItemFailures"] = batch_item_failures
        return sqs_batch_response
