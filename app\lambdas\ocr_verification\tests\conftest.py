import pytest
import boto3
from moto import mock_aws
import json
from pathlib import Path
from dotenv import load_dotenv
import sys
import os

TEST_BUCKET = "test-bucket"
PROMPT_DIR = "prompts"
TABLE_NAME = "test_request_tracking"


@pytest.fixture(scope="session", autouse=True)
def load_test_env():
    env_path = Path(__file__).resolve().parent / "../.env.test"
    load_dotenv(dotenv_path=env_path, override=True)


@pytest.fixture
def s3():
    with mock_aws():
        s3_client = boto3.client("s3", region_name="us-east-1")
        s3_client.create_bucket(Bucket=TEST_BUCKET)
        yield s3_client


@pytest.fixture
def ssm():
    with mock_aws():
        ssm_client = boto3.client("ssm", region_name="us-east-1")
        yield ssm_client


@pytest.fixture
def setup_moto_env(tmp_path_factory, s3, ssm):
    # ---- Set mock SSM parameters ----
    ssm.put_parameter(
        Name="/claims-verification-dev/s3/artifacts", Value=TEST_BUCKET, Type="String"
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/s3/artifacts/prompts",
        Value=f"{PROMPT_DIR}/",
        Type="String",
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/dynamodb/table",
        Value="mock_table",
        Type="String",
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/bedrock/region", Value="us-east-1", Type="String"
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/queue/notification",
        Value="queue-url",
        Type="String",
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/dynamodb/ttl", Value="3600", Type="String"
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/bedrock/inference/config",
        Value="{}",
        Type="String",
    )
    ssm.put_parameter(
        Name="/claims-verification-dev/bedrock/maximage/size",
        Value="1000",
        Type="String",
    )

    # ---- Upload mock S3 prompt ----
    s3.put_object(
        Bucket=TEST_BUCKET,
        Key=f"{PROMPT_DIR}/extract_claims_from_text_pdf.txt",
        Body="Mocked prompt text",
    )

    # ---- Create local mock prompts.json ----
    prompts_path = tmp_path_factory.mktemp("data") / "prompts.json"
    with open(prompts_path, "w") as f:
        json.dump(
            {"extract_claims_from_text_pdf": "extract_claims_from_text_pdf.txt"}, f
        )

    # ---- Create local mock ssm_config.json ----
    ssm_config_path = tmp_path_factory.mktemp("data") / "ssm_config.json"
    with open(ssm_config_path, "w") as f:
        json.dump(
            {
                "DEV_SSM": {
                    "s3_bucket_artifacts": "/claims-verification-dev/s3/artifacts",
                    "s3_bucket_artifacts_prompts": "/claims-verification-dev/s3/artifacts/prompts",
                    "dynamodb_table_name": "/claims-verification-dev/dynamodb/table",
                    "bedrock_model_region": "/claims-verification-dev/bedrock/region",
                    "notification_success_queue_url": "/claims-verification-dev/queue/notification",
                    "dynamodb_record_ttl": "/claims-verification-dev/dynamodb/ttl",
                    "bedrock_inference_config": "/claims-verification-dev/bedrock/inference/config",
                    "bedrock_max_image_size": "/claims-verification-dev/bedrock/maximage/size",
                }
            },
            f,
        )

    return {"prompts_file": str(prompts_path), "ssm_config_file": str(ssm_config_path)}


@pytest.fixture(scope="function")
def dynamodb_mock():
    with mock_aws():
        # Set up DynamoDB
        dynamodb = boto3.resource("dynamodb")
        table = dynamodb.create_table(
            TableName=TABLE_NAME,
            KeySchema=[{"AttributeName": "RequestID", "KeyType": "HASH"}],
            AttributeDefinitions=[{"AttributeName": "RequestID", "AttributeType": "S"}],
            ProvisionedThroughput={"ReadCapacityUnits": 5, "WriteCapacityUnits": 5},
        )
        table.wait_until_exists()

        # Monkeypatch the global table object used in the actual code
        import core.utils.utils as utils

        utils.dynamodb_table_name = table

        yield table
