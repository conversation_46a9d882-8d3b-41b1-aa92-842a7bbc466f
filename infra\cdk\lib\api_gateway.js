"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicQualityApiGateway = void 0;
const constructs_1 = require("constructs");
const apigateway = __importStar(require("aws-cdk-lib/aws-apigateway"));
const iam = __importStar(require("aws-cdk-lib/aws-iam"));
const cdk = __importStar(require("aws-cdk-lib"));
/**
 * Sets up a basic API Gateway with SQS integration.
 * Exposes POST / that sends messages to existing SQS queue.
 */
class BasicQualityApiGateway extends constructs_1.Construct {
    constructor(scope, id, props) {
        super(scope, id);
        // Get current account ID and region
        const account = cdk.Stack.of(this).account;
        const region = cdk.Stack.of(this).region;
        // Create the API Gateway REST API
        this.api = new apigateway.RestApi(this, 'QualityResponseApi', {
            restApiName: `ocr-verification-api-${props.stage}`,
            deployOptions: {
                stageName: props.stage
            },
            endpointTypes: [apigateway.EndpointType.REGIONAL],
            description: 'OCR Verification API with SQS integration'
        });
        // Reference existing IAM role
        const existingRoleName = `ocr-verification-api-role-${props.stage}`;
        const apiGatewayRole = iam.Role.fromRoleName(this, 'ExistingApiGatewayRole', existingRoleName);
        // Create SQS integration for existing queue
        const sqsIntegration = new apigateway.AwsIntegration({
            service: 'sqs',
            path: `${account}/${props.sqsQueueName}`,
            integrationHttpMethod: 'POST',
            options: {
                credentialsRole: apiGatewayRole,
                requestParameters: {
                    'integration.request.header.Content-Type': "'application/x-www-form-urlencoded'"
                },
                requestTemplates: {
                    'application/json': 'Action=SendMessage&MessageBody=$util.urlEncode($input.body)'
                },
                integrationResponses: [
                    {
                        statusCode: '200',
                        responseTemplates: {
                            'application/json': JSON.stringify({
                                message: "Message sent to SQS successfully",
                                timestamp: "$context.requestTime",
                                requestId: "$context.requestId"
                            })
                        }
                    },
                    {
                        statusCode: '400',
                        selectionPattern: '4\\d{2}',
                        responseTemplates: {
                            'application/json': JSON.stringify({
                                error: "Bad Request",
                                message: "Invalid request format"
                            })
                        }
                    },
                    {
                        statusCode: '500',
                        selectionPattern: '5\\d{2}',
                        responseTemplates: {
                            'application/json': JSON.stringify({
                                error: "Internal Server Error",
                                message: "Failed to send message to SQS"
                            })
                        }
                    }
                ]
            }
        });
        // Add POST method directly to the root resource (no sub-resources)
        this.api.root.addMethod('POST', sqsIntegration, {
            methodResponses: [
                {
                    statusCode: '200',
                    responseModels: {
                        'application/json': apigateway.Model.EMPTY_MODEL
                    }
                },
                {
                    statusCode: '400',
                    responseModels: {
                        'application/json': apigateway.Model.ERROR_MODEL
                    }
                },
                {
                    statusCode: '500',
                    responseModels: {
                        'application/json': apigateway.Model.ERROR_MODEL
                    }
                }
            ],
            apiKeyRequired: false
        });
    }
}
exports.BasicQualityApiGateway = BasicQualityApiGateway;
//# sourceMappingURL=data:application/json;base64,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