"""
Module for interacting with Google Gemini LLM models using the new Google Gen AI Python SDK.
"""

import json
import time
import io
import os

from PIL import Image
from google import genai
from google.genai import types

from config.config import config
from core.enums.enums import ResponseStatus
from core.logger.logger import get_logger, log_frame_info, log_frame_error, log_frame_debug, log_frame_warning
from core.utils.utils import  get_json_value

# Initialize logger for this module
logger = get_logger(__name__)

# Global configuration and client initialization
config_obj = config.ssm_config

# Ensure config_obj is a dictionary
if not isinstance(config_obj, dict):
    log_frame_warning(logger, "config_obj is not a dictionary, using empty config")
    config_obj_safe = {}
else:
    config_obj_safe = config_obj

gemini_api_key = config_obj_safe.get("gemini_api_key")

# Retry configuration
gemini_max_attempts = 3

# Initialize Gemini client using the new SDK
gemini_client = None
if gemini_api_key:
    try:
        # Set the API key as environment variable for the new SDK
        os.environ['GEMINI_API_KEY'] = gemini_api_key
        # Create client with explicit API key
        gemini_client = genai.Client(api_key=gemini_api_key)
        log_frame_info(logger, message="Gemini client initialized successfully with new SDK")
    except Exception as e:
        log_frame_error(logger, message=f"Failed to initialize Gemini client: {str(e)}")
        gemini_client = None
else:
    log_frame_error(logger, message="No Gemini API key found in configuration")


class GeminiLlm:
    """
    A class for interacting with Google Gemini LLM models using the new Google Gen AI Python SDK.
    """

    @classmethod
    def _create_dynamic_function_schema(cls, business_rules, has_image=False):
        """
        Create a dynamic function schema using the new SDK's function declaration approach.
        """

        if has_image:
            # Create function declaration for image-based extraction with quality assessment
            function_declaration = types.FunctionDeclaration(
                name="extract_document_data",
                description=f"Extract {len(business_rules)} specific business data fields and assess image quality from a document image",
                parameters={
                    "type": "object",
                    "properties": {
                        "business_rules": {
                            "type": "object",
                            "description": "Extracted business rule field names and their values from the document",
                            "properties": {
                                rule: {"type": "string", "description": f"Extracted value for {rule.replace('_', ' ')} field"}
                                for rule in business_rules
                            },
                            "required": business_rules
                        }
                    },
                    "required": ["business_rules"]
                }
            )
        else:
            # Create function declaration for text-based extraction
            function_declaration = types.FunctionDeclaration(
                name="extract_text_data",
                description=f"Extract {len(business_rules)} specific business data fields from text content",
                parameters={
                    "type": "object",
                    "properties": {
                        "business_rules": {
                            "type": "object",
                            "description": "Extracted business rule field names and their values from the text",
                            "properties": {
                                rule: {"type": "string", "description": f"Extracted value for {rule.replace('_', ' ')} field"}
                                for rule in business_rules
                            },
                            "required": business_rules
                        }
                    },
                    "required": ["business_rules"]
                }
            )

        return function_declaration

    @classmethod
    def converse_model(cls, messages=[], business_rules=None):
        try:
            if not gemini_client:
                return {"status": ResponseStatus.FAIL.value, "message": "Gemini client not initialized"}

            start_time = time.time()
            inference_config = config_obj_safe.get("gemini_inference_config", {})
            model_id = str(get_json_value(inference_config, "gemini_model_name")) or "gemini-2.5-flash"

            # Configuration for the new SDK
            config_kwargs = {
                "max_output_tokens": int(get_json_value(inference_config, "gemini_max_tokens") or 4096),
                "temperature": float(get_json_value(inference_config, "gemini_temperature") or 0.0),
                "top_p": float(get_json_value(inference_config, "gemini_top_p") or 0.95),
                "candidate_count": int(get_json_value(inference_config, "gemini_candidate_count") or 1),
            }
            log_frame_info(logger, f"Using model: {model_id} (temp: {config_kwargs['temperature']}, max_tokens: {config_kwargs['max_output_tokens']})")

            # Set up function calling if business rules are provided
            tools = None
            tool_config = None
            if business_rules:
                try:
                    has_image = any("image" in item for msg in messages for item in msg.get("content", []))
                    function_declaration = cls._create_dynamic_function_schema(business_rules, has_image)

                    # Wrap function declaration in a Tool object
                    tool = types.Tool(function_declarations=[function_declaration])
                    tools = [tool]

                    # Configure function calling to use ANY mode for reliable function calls
                    tool_config = types.ToolConfig(
                        function_calling_config=types.FunctionCallingConfig(mode='ANY')
                    )

                    log_frame_info(logger, f"Function calling enabled: {function_declaration.name} ({len(business_rules)} fields)")
                except Exception as e:
                    log_frame_error(logger, f"Function calling setup failed: {e}")
                    return {"status": ResponseStatus.FAIL.value, "message": f"Function calling setup failed: {e}"}

            # Convert messages to the new SDK format
            contents = cls._convert_messages_to_gemini_format(messages)

            response = None
            for attempt in range(gemini_max_attempts):
                try:
                    # Use the new SDK's generate_content method
                    response = gemini_client.models.generate_content(
                        model=model_id,
                        contents=contents,
                        config=types.GenerateContentConfig(
                            tools=tools,
                            tool_config=tool_config,
                            automatic_function_calling=types.AutomaticFunctionCallingConfig(disable=True),
                            **config_kwargs
                        )
                    )
                    break
                except Exception as e:
                    if attempt == gemini_max_attempts - 1:
                        raise e
                    log_frame_debug(logger, message=f"Retry attempt {attempt + 1} failed: {e}")
                    time.sleep(2 ** attempt)

            if response is None:
                raise ValueError("No response from Gemini model after retries.")

            result = ""
            # Prioritize extracting function call arguments if tools were used
            if business_rules and tools:
                extracted = cls._extract_function_call_arguments(response)
                if extracted is not None:
                    try:
                        result = json.dumps(extracted, ensure_ascii=False)
                        log_frame_info(logger, "Function call successful - JSON serialization completed")
                        log_frame_debug(logger, f"Function call result length: {len(result)} characters")
                    except (TypeError, ValueError) as e:
                        log_frame_error(logger, f"Failed to serialize function call result to JSON: {e}")
                        log_frame_error(logger, f"Raw extracted data: {extracted}")
                        return {"status": ResponseStatus.FAIL.value, "message": f"Function call result serialization failed: {e}"}
                else:
                    # Fallback to text if no function call was returned
                    log_frame_error(logger, "Function calling failed - no function call found.")
                    if hasattr(response, 'text') and response.text:
                        result = response.text
                        log_frame_info(logger, "Falling back to text response")
                    else:
                        return {"status": ResponseStatus.FAIL.value, "message": "No function call or text response received"}
            elif hasattr(response, 'text') and response.text:
                result = response.text
                log_frame_info(logger, "Using text response (no function calling)")
            else:
                return {"status": ResponseStatus.FAIL.value, "message": "No response content received"}

            if not result:
                log_frame_error(logger, message="Empty response from Gemini model after parsing.")
                return {"status": ResponseStatus.FAIL.value, "message": "Empty response from Gemini model"}

            # Log raw result for debugging (truncated if too long)
            result_preview = result[:500] + "..." if len(result) > 500 else result
            log_frame_debug(logger, f"Raw Gemini result: {result_preview}")

            # Normalize JSON output for consistency with existing codebase
            if result.strip().startswith('{'):
                try:
                    parsed = json.loads(result)
                    if "business_rules" not in parsed:
                        parsed = {"business_rules": parsed}
                    result = json.dumps(parsed, ensure_ascii=False)
                    log_frame_debug(logger, "JSON normalization successful")
                except json.JSONDecodeError as e:
                    log_frame_error(logger, f"JSON normalization failed: {e}")
                    log_frame_error(logger, f"Problematic JSON at position {e.pos}: {result[max(0, e.pos-50):e.pos+50]}")
                    # Continue with original result if normalization fails

            # Extract usage information using the new SDK format
            usage_metadata = getattr(response, 'usage_metadata', None)
            if usage_metadata:
                llm_usage = {
                    "inputTokens": getattr(usage_metadata, 'prompt_token_count', 0),
                    "outputTokens": getattr(usage_metadata, 'candidates_token_count', 0),
                    "totalTokens": getattr(usage_metadata, 'total_token_count', 0),
                    "latencyMs": int((time.time() - start_time) * 1000),
                }
            else:
                llm_usage = {
                    "inputTokens": 0,
                    "outputTokens": 0,
                    "totalTokens": 0,
                    "latencyMs": int((time.time() - start_time) * 1000),
                }
            log_frame_info(logger, f"Gemini response: {len(result)} chars, {llm_usage['totalTokens']} tokens")

            return {"status": ResponseStatus.SUCCESS.value, "result": result, "llm_usage": llm_usage}

        except Exception as e:
            error_message = str(e)
            if "RATE_LIMIT_EXCEEDED" in error_message or "quota" in error_message.lower():
                log_frame_error(logger, message="Gemini model throttled:", error=error_message)
                return {"status": ResponseStatus.THROTTLE.value, "message": "Rate limit exceeded"}
            else:
                log_frame_error(logger, message="Error invoking Gemini model:", error=error_message)
                return {"status": ResponseStatus.FAIL.value, "message": f"ERROR: {error_message}"}

    @classmethod
    def _extract_function_call_arguments(cls, response):
        """
        Extract function call arguments from the new SDK response format with robust error handling.
        """
        try:
            # Check if response has function_calls attribute (new SDK format)
            if hasattr(response, 'function_calls') and response.function_calls:
                function_call = response.function_calls[0]  # Get first function call
                if hasattr(function_call, 'args'):
                    # Convert args to a standard Python dict with validation
                    args_dict = dict(function_call.args)
                    log_frame_debug(logger, f"Extracted function call args: {args_dict}")
                    return cls._validate_and_sanitize_function_args(args_dict)

            # Fallback: check candidates for function calls (legacy format)
            for candidate in getattr(response, 'candidates', []):
                for part in getattr(candidate.content, 'parts', []):
                    if hasattr(part, 'function_call') and part.function_call:
                        if hasattr(part.function_call, 'args'):
                            args_dict = dict(part.function_call.args)
                            log_frame_debug(logger, f"Extracted function call args (legacy): {args_dict}")
                            return cls._validate_and_sanitize_function_args(args_dict)
        except Exception as e:
            log_frame_error(logger, f"Error extracting function call arguments: {e}")
        return None

    @classmethod
    def _validate_and_sanitize_function_args(cls, args_dict):
        """
        Validate and sanitize function call arguments to prevent JSON parsing errors.

        Args:
            args_dict: Dictionary of function call arguments

        Returns:
            Sanitized dictionary or None if validation fails
        """
        try:
            if not isinstance(args_dict, dict):
                log_frame_error(logger, f"Function args is not a dictionary: {type(args_dict)}")
                return None

            sanitized = {}
            for key, value in args_dict.items():
                # Sanitize string values that might contain problematic characters
                if isinstance(value, str):
                    # Remove or escape problematic characters that could break JSON
                    sanitized_value = value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    # Remove excessive whitespace
                    sanitized_value = ' '.join(sanitized_value.split())
                    # Escape quotes that might break JSON structure
                    sanitized_value = sanitized_value.replace('"', '\\"')
                    sanitized[key] = sanitized_value
                elif isinstance(value, (int, float, bool)) or value is None:
                    sanitized[key] = value
                elif isinstance(value, dict):
                    # Recursively sanitize nested dictionaries
                    nested_sanitized = cls._validate_and_sanitize_function_args(value)
                    if nested_sanitized is not None:
                        sanitized[key] = nested_sanitized
                    else:
                        log_frame_error(logger, f"Failed to sanitize nested dict for key: {key}")
                        sanitized[key] = {}
                elif isinstance(value, list):
                    # Sanitize list items
                    sanitized_list = []
                    for item in value:
                        if isinstance(item, str):
                            sanitized_item = item.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                            sanitized_item = ' '.join(sanitized_item.split())
                            sanitized_item = sanitized_item.replace('"', '\\"')
                            sanitized_list.append(sanitized_item)
                        else:
                            sanitized_list.append(item)
                    sanitized[key] = sanitized_list
                else:
                    # Convert other types to string and sanitize
                    str_value = str(value)
                    sanitized_value = str_value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
                    sanitized_value = ' '.join(sanitized_value.split())
                    sanitized_value = sanitized_value.replace('"', '\\"')
                    sanitized[key] = sanitized_value
                    log_frame_debug(logger, f"Converted {type(value)} to sanitized string for key: {key}")

            # Validate that the sanitized dict can be JSON serialized
            try:
                json.dumps(sanitized, ensure_ascii=False)
                log_frame_debug(logger, "Function args validation successful")
                return sanitized
            except (TypeError, ValueError) as e:
                log_frame_error(logger, f"Sanitized args still not JSON serializable: {e}")
                return None

        except Exception as e:
            log_frame_error(logger, f"Error validating function args: {e}")
            return None

    @classmethod
    def _convert_messages_to_gemini_format(cls, messages):
        """
        Convert messages to the new SDK format.
        """
        contents = []
        for message in messages:
            role = message.get("role")
            parts = []

            for content_item in message.get("content", []):
                if "text" in content_item:
                    # Add text part using the correct method
                    parts.append(types.Part(text=content_item["text"]))
                elif "image" in content_item:
                    # Add image part
                    image_data = content_item["image"]
                    if "source" in image_data and "bytes" in image_data["source"]:
                        try:
                            img = Image.open(io.BytesIO(image_data["source"]["bytes"]))
                            # Convert PIL image to base64 for the new SDK
                            img_buffer = io.BytesIO()
                            img.save(img_buffer, format='PNG')
                            img_bytes = img_buffer.getvalue()

                            # Create image part with proper data format
                            image_part = types.Part(
                                inline_data=types.Blob(
                                    mime_type="image/png",
                                    data=img_bytes
                                )
                            )
                            parts.append(image_part)
                        except Exception as e:
                            log_frame_error(logger, f"Could not process image bytes: {e}")

            if role in ["user", "model"] and parts:
                contents.append(types.Content(role=role, parts=parts))

        return contents