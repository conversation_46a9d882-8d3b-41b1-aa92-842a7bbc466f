# Listing Quality Service

The Listing Quality Service is an AWS-based system that processes listing quality assessments using AWS Lambda functions and SQS queues for distributed processing, prioritization, and reliability.

This OCR verification is an AWS-bases system that process the OCR verification of different documents. It supports both text-based and scanned PDFs, extracting relevant informationfrom the documents based on specified input fields. The extracted data is then compared against the values provided in the request payload, and a result of either ACCEPTED or REJECTED is returned based on the comparison.

## Architecture Overview

The service uses a serverless architecture built with AWS CDK with the following components:

- **Request Queuing System**: Contains the request that needs OCR verification
- **Processing Pipeline**: Does the OCR verification including data extraction from text/scanned PDFs and verification of extracted data with provided ones.
- **AI Integration**: Uses Amazon Bedrock with Nova Pro Model for OCR related matters
- **Monitoring & Error Handling**: Tracks request lifecycles and reports failures

## Infrastructure

The infrastructure is defined using AWS CDK with TypeScript and consists of:

- Lambda functions for ocr verification
- SQS queues for requests handling and retry mechanism
- CloudWatch for monitoring
- DynamoDB for tracking request state
- Parameter Store for configuration
- S3 for prompts storage

## GitHub Actions Workflows

### 1. Upload-To-S3

Uploads prompt files to S3 when a PR comment contains `.upload`.

```yaml
# Triggered by PR comments containing '.upload'
# Uploads prompt templates to S3 bucket
# Verifies the upload completion
```

### 3. Deploy-PR

Deploys the AWS CDK stack when triggered through branch-deploy comments.

```yaml
# Triggered by PR comments containing '.deploy'
# Uses GitHub's branch-deploy for deployment management
# Runs CDK deployment to update AWS resources
# Sends Slack notifications on completion
```

## Project Structure

```
.
├── app/
│   └── prompts/          # Prompt templates uploaded to S3
│   ├── lambdas/
│		├── ocr-verification  # Main OCR related code resides in this lambda
│		├── layers            # Layers to be added with ocr-verification lambda
│			├── ocr-packages-layer
│			├── ocr-poppler-layer
├── infra/
	└── cdk/
		├── lib/
		│   ├── stack.ts 	  # CDK stack definition
		│   └── parameters.ts     # SSM Parameter Store configuration
		│   └── api_gateway.ts    # API Gateway configuration
└── cdk.json             # CDK configuration
```

## Deployment

The project uses GitHub Actions for its CI/CD pipeline:

1. **PR Comments**:
   - `.upload`: Upload config and prompts to S3
   - `.deploy`: Deploy the CDK stack

2. **Environments**:
   - The code is configured for a `development` environment

## Configuration

Configuration is managed through AWS SSM Parameter Store with parameters defined in `parameters.ts`:

- Bedrock model configuration
- S3 bucket locations for artifacts and tracking
- SQS queue URLs
- DynamoDB table settings

## Gemini LLM Provider

The system supports both Gemini (default) and Bedrock LLM providers. To switch between providers:

### Quick Switch Commands

**Switch to Gemini (Development)**
```powershell
# 1. Set API key first
aws ssm put-parameter `
  --name "/ocr-verification/development/gemini/api_key" `
  --value "AIzaSyA1Ucxi78kMBranYeQMCLqA_nsSPBiSN2Q" `
  --type "SecureString" `
  --overwrite `
  --region ap-southeast-1

# 2. Switch provider
aws ssm put-parameter `
  --name "/ocr-verification/development/llm_provider" `
  --value "gemini" `
  --overwrite `
  --region ap-southeast-1
```

**Switch back to Bedrock**
```powershell
aws ssm put-parameter `
  --name "/ocr-verification/development/llm_provider" `
  --value "bedrock" `
  --overwrite `
  --region ap-southeast-1
```

**Verify current provider**
```powershell
aws ssm get-parameter `
  --name "/ocr-verification/development/llm_provider" `
  --region ap-southeast-1
```

📖 **For complete documentation**: See [`docs/GEMINI_INTEGRATION.md`](app/lambdas/ocr_verification/docs/GEMINI_INTEGRATION.md)

## Development

To develop and deploy locally:

1. Set up AWS credentials
2. Install dependencies: `npm install`
3. Build the project: `npm run build`
4. Deploy the stack: `cdk deploy`

## Monitoring and Logging

- CloudWatch Logs are configured for Bedrock invocations
- Request tracking is stored in DynamoDB with configurable TTL
- Requests statuses are stored in DynamoDB along with Notification queue
