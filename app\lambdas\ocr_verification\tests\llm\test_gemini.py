import pytest
import json
import os
from unittest.mock import patch, MagicMock, DEFAULT


# Mock environment before any imports
@pytest.fixture(scope="module", autouse=True)
def mock_env():
    """Mock environment variables"""
    with patch.dict(
        os.environ,
        {
            "ENV": "development",
            "DEFAULT_REGION": "us-east-1",
            "GEMINI_API_KEY": "test-gemini-key",
        },
    ):
        yield


@pytest.fixture(scope="module")
def mock_config_before_import():
    """Mock config module before gemini import"""
    mock_config = MagicMock()

    # Mock gemini inference config JSON string
    gemini_inference_config = {
        "gemini_model_name": "gemini-2.5-flash",
        "gemini_max_tokens": "4096",
        "gemini_temperature": "0.0",
        "gemini_top_p": "0.95",
        "gemini_candidate_count": "1",
        "max_attempts": "2",
    }

    # Mock ssm_config
    mock_config.ssm_config = {
        "gemini_api_key": "test-gemini-key",
        "gemini_inference_config": json.dumps(gemini_inference_config),
    }

    with patch("config.config.config", mock_config):
        yield mock_config


@pytest.fixture(scope="module")
def mock_gemini_before_import():
    """Mock google.genai before gemini import"""
    with patch("google.genai.Client") as mock_client_class:
        # Create mock client instance
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Create mock models
        mock_models = MagicMock()
        mock_client.models = mock_models

        yield mock_client_class, mock_client, mock_models


@pytest.fixture(scope="module")
def mock_utils_before_import():
    """Mock utils functions before gemini import"""
    with patch("core.utils.utils.get_json_value") as mock_get_json_value:
        # Configure get_json_value to return appropriate values based on key
        def get_json_value_side_effect(json_str, key):
            if not json_str:
                return None
            try:
                data = json.loads(json_str) if isinstance(json_str, str) else json_str
                return data.get(key)
            except:
                return None

        mock_get_json_value.side_effect = get_json_value_side_effect
        yield mock_get_json_value


@pytest.fixture(scope="module")
def mock_logger_before_import():
    """Mock logger functions before gemini import"""
    with (
        patch("core.logger.logger.get_logger") as mock_get_logger,
        patch("core.logger.logger.log_frame_info") as mock_log_info,
        patch("core.logger.logger.log_frame_warning") as mock_log_warning,
        patch("core.logger.logger.log_frame_debug") as mock_log_debug,
        patch("core.logger.logger.log_frame_error") as mock_log_error,
    ):
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger

        yield {
            "get_logger": mock_get_logger,
            "log_info": mock_log_info,
            "log_warning": mock_log_warning,
            "log_debug": mock_log_debug,
            "log_error": mock_log_error,
            "logger": mock_logger,
        }


@pytest.fixture(scope="module")
def mock_enums_before_import():
    """Mock enums before gemini import"""
    with patch("core.enums.enums.ResponseStatus") as mock_response_status:
        # Create mock enum values
        mock_response_status.SUCCESS.value = "SUCCESS"
        mock_response_status.FAIL.value = "FAIL"
        mock_response_status.THROTTLE.value = "THROTTLE"

        yield mock_response_status


# Import gemini only after all mocking is done
@pytest.fixture(scope="module")
def gemini_module(
    mock_config_before_import,
    mock_gemini_before_import,
    mock_utils_before_import,
    mock_logger_before_import,
    mock_enums_before_import,
):
    """Import gemini module after mocking"""
    # Clear any existing module from cache
    import sys

    modules_to_clear = [mod for mod in sys.modules.keys() if "gemini" in mod]
    for mod in modules_to_clear:
        if mod in sys.modules:
            del sys.modules[mod]

    # Import the gemini module
    from core.llm.gemini import GeminiLlm, gemini_client, gemini_max_attempts
    import core.llm.gemini as gemini_module

    yield gemini_module


def test_gemini_initialization(
    gemini_module, mock_config_before_import, mock_gemini_before_import
):
    """Test gemini module initialization"""
    mock_client_class, mock_client, mock_models = mock_gemini_before_import

    # Test that gemini_max_attempts is set correctly
    assert gemini_module.gemini_max_attempts == 2

    # Test that Gemini client was created
    mock_client_class.assert_called_with(api_key="test-gemini-key")


def test_converse_model_success(
    gemini_module, mock_gemini_before_import, mock_logger_before_import
):
    """Test successful converse_model call"""
    mock_client_class, mock_client, mock_models = mock_gemini_before_import
    mock_loggers = mock_logger_before_import

    # Mock successful gemini response
    mock_response = MagicMock()
    mock_response.text = "This is the AI response from Gemini"
    
    # Mock usage metadata
    mock_usage = MagicMock()
    mock_usage.prompt_token_count = 100
    mock_usage.candidates_token_count = 50
    mock_usage.total_token_count = 150
    mock_response.usage_metadata = mock_usage

    mock_models.generate_content.return_value = mock_response

    # Test messages
    test_messages = [{"role": "user", "content": [{"text": "Hello, how are you?"}]}]

    # Call the method
    result = gemini_module.GeminiLlm.converse_model(test_messages)

    # Verify the response
    assert result["status"] == "SUCCESS"
    assert result["result"] == "This is the AI response from Gemini"
    assert result["llm_usage"]["inputTokens"] == 100
    assert result["llm_usage"]["outputTokens"] == 50
    assert result["llm_usage"]["totalTokens"] == 150

    # Verify gemini client was called
    mock_models.generate_content.assert_called_once()


def test_converse_model_without_usage_metrics(gemini_module, mock_gemini_before_import):
    """Test converse_model when usage metrics are not available"""
    mock_client_class, mock_client, mock_models = mock_gemini_before_import

    # Mock response without usage metadata
    mock_response = MagicMock()
    mock_response.text = "Response without usage metrics"
    mock_response.usage_metadata = None

    mock_models.generate_content.return_value = mock_response

    test_messages = [{"role": "user", "content": [{"text": "Test message"}]}]

    result = gemini_module.GeminiLlm.converse_model(test_messages)

    assert result["status"] == "SUCCESS"
    assert result["result"] == "Response without usage metrics"
    assert result["llm_usage"]["inputTokens"] == 0
    assert result["llm_usage"]["outputTokens"] == 0
    assert result["llm_usage"]["totalTokens"] == 0


def test_converse_model_rate_limit_exception(
    gemini_module, mock_gemini_before_import, mock_logger_before_import
):
    """Test converse_model with rate limit exception"""
    mock_client_class, mock_client, mock_models = mock_gemini_before_import
    mock_loggers = mock_logger_before_import

    # Mock rate limit exception
    mock_models.generate_content.side_effect = Exception("RATE_LIMIT_EXCEEDED")

    test_messages = [{"role": "user", "content": [{"text": "Test message"}]}]

    result = gemini_module.GeminiLlm.converse_model(test_messages)

    assert result["status"] == "THROTTLE"
    assert "rate limiting" in result["message"]


def test_converse_model_general_exception(
    gemini_module, mock_gemini_before_import, mock_logger_before_import
):
    """Test converse_model with general exception"""
    mock_client_class, mock_client, mock_models = mock_gemini_before_import
    mock_loggers = mock_logger_before_import

    # Mock general exception
    mock_models.generate_content.side_effect = Exception("General error")

    test_messages = [{"role": "user", "content": [{"text": "Test message"}]}]

    result = gemini_module.GeminiLlm.converse_model(test_messages)

    assert result["status"] == "FAIL"
    assert "General error" in result["message"]


def test_convert_messages_format(gemini_module):
    """Test message conversion to Gemini format"""
    # Test text-only message
    messages = [{"role": "user", "content": [{"text": "Hello world"}]}]
    
    result = gemini_module.GeminiLlm._convert_messages_to_gemini_format(messages)
    
    assert len(result) == 1
    assert result[0] == "Hello world"


def test_gemini_llm_class_exists(gemini_module):
    """Test that GeminiLlm class exists and has expected methods"""
    GeminiLlm = gemini_module.GeminiLlm

    # Test class exists
    assert GeminiLlm is not None

    # Test required methods exist
    assert hasattr(GeminiLlm, "converse_model")
    assert callable(GeminiLlm.converse_model)
    assert hasattr(GeminiLlm, "_convert_messages_to_gemini_format")
    assert callable(GeminiLlm._convert_messages_to_gemini_format) 