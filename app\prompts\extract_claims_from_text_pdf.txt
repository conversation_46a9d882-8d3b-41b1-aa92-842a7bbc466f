Human: You are an expert in extracting structured information from text, including text extracted from scanned PDF images via OCR. Given the following text, extract the following parameters and return them in a JSON format:

business rules{parameter_list}

## Requirements:

1. Format the output as a JSON object with the following structure:
```json
{{ 
  "business_rules": {{
{parameter_fields}
  }}
}}
```

**Core Principles:**

   - Extract data exactly as it appears without translation
   - Only extract actual values, not field labels/headings

**Strictly Prohibited:**

   - No translation between languages
   - No inferring missing values
   - No mirroring values across languages
   - No extraction of field labels as values
   - No units/currency symbols should be retrived with the number fields like (Area (SqMt, SqM) or Currency (AED), etc.)

## Previously extracted data from earlier chunks:
```json
{existing_data}
```

Please use this information to ensure consistency across chunks, but prioritize new information found in the current text. If you find new information that contradicts previous data, prefer the new information.

## Text to process:
{raw_text}

Return the result as a properly formatted JSON object following the structure described above. 
    - In the <scratchpad> tag, provide a clear and concise bullet-point analysis for each business rule field. For each field, state:
        - Whether a valid English value is present — only if it is explicitly found in the raw text (not inferred or translated).
        - Whether a valid Arabic value is present — only if it is explicitly found in the raw text (not inferred or translated).
        - Whether numeric IDs are detected and any associated units are correctly removed.
        - Whether numeric IDs are consistently included in both the English and Arabic fields, as numeric values are language-independent.
    - Follow above rules strictly: do not infer, assume, or translate any text.
    - Then, in the <json> tag, return only the valid JSON output according to the required structure, without any extra explanation or formatting.
    - Return both tags <scratchpad> and <json> in your response.
    - The <json> tag must contain **only valid JSON** as specified.

Note: You are an information extractor, not a translator. Do not translate any text under any circumstance. Only extract values exactly as they appear in the input.
Assistant: