# Textract-Based Quality Assessment

This document describes the new AWS Textract-based quality assessment system that replaces the previous LLM-based quality assessment for more objective and reliable document quality evaluation.

## Overview

The quality assessment system now uses AWS Textract's OCR confidence scores to determine document quality and routing decisions. This provides:

- **Objective metrics**: Based on actual OCR performance rather than subjective LLM evaluation
- **Consistent results**: Same document will always get the same quality score
- **Cost effective**: No additional LLM calls for quality assessment
- **Fast processing**: Direct OCR confidence analysis
- **Detailed metrics**: Multiple confidence calculation methods

## Architecture

### Quality Assessment Flow

```
Document/Image → Textract OCR → Confidence Scores → Quality Metrics → Routing Decision
```

### Key Components

1. **Textract Quality Module** (`core/services/textract_quality.py`)
   - `assess_document_quality_with_textract()`: Single image assessment
   - `assess_pdf_quality_with_textract()`: Multi-page PDF assessment
   - `calculate_confidence_metrics()`: Statistical confidence analysis

2. **Integration Points**
   - `process.py`: Modified to use Textract instead of LLM quality assessment
   - Configuration: SSM parameters for confidence thresholds
   - CDK Infrastructure: Added Textract confidence threshold parameters

## Quality Metrics

### Confidence Calculation Methods

The system calculates multiple confidence metrics from Textract's word-level confidence scores:

1. **Geometric Mean** (Primary Metric)
   - Best for combining probabilities
   - Most reliable for OCR confidence assessment
   - Formula: `(product of all confidences)^(1/n)`

2. **Arithmetic Mean**
   - Simple average of all confidence scores
   - Good for general understanding

3. **Harmonic Mean**
   - More conservative than arithmetic mean
   - Penalizes low confidence scores more heavily

4. **Statistical Measures**
   - Minimum/Maximum confidence
   - Median confidence
   - Standard deviation (variability measure)

### Quality Levels

Based on geometric mean confidence:

| Confidence Range | Quality Level | Quality Score | Description |
|------------------|---------------|---------------|-------------|
| 90%+ | excellent | 1.0 | Perfect OCR performance |
| 80-89% | good | 0.8 | High quality, reliable extraction |
| 70-79% | fair | 0.6 | Acceptable quality, minor issues |
| 60-69% | poor | 0.4 | Low quality, significant issues |
| <60% | very_poor | 0.2 | Very low quality, unreliable |

### Routing Decision

Documents are routed based on configurable confidence threshold:

- **Default threshold**: 60% geometric mean confidence
- **Configurable via SSM**: `/ocr-verification/{env}/textract/confidence/threshold`
- **Acceptable**: `geometric_mean >= threshold`
- **Rejected**: `geometric_mean < threshold`

## Configuration

### SSM Parameters

```json
{
  "textract_confidence_threshold": "/ocr-verification/{env}/textract/confidence/threshold"
}
```

### CDK Infrastructure

The following parameters are automatically created:

```typescript
// Textract confidence threshold parameter
createParameterWithTags('TextractConfidenceThreshold', {
  parameterName: `${paramPrefix}/textract/confidence/threshold`,
  stringValue: '60',
  description: 'Textract confidence threshold for document quality assessment',
  tier: ssm.ParameterTier.STANDARD,
});
```

### IAM Permissions

The Lambda function requires the following Textract permissions:

```typescript
// Create IAM policy for Textract permissions
const textractPolicy = new iam.Policy(this, 'TextractPolicy', {
  policyName: `ocr-verification-textract-policy-${resourceNameSuffix}`,
  statements: [
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'textract:DetectDocumentText',
        'textract:AnalyzeDocument',
        'textract:GetDocumentAnalysis',
        'textract:GetDocumentTextDetection',
        'textract:StartDocumentAnalysis',
        'textract:StartDocumentTextDetection'
      ],
      resources: ['*']
    })
  ]
});

// Attach the Textract policy to the existing role
textractPolicy.attachToRole(requestProcessingRole);
```

**Required Permissions:**
- `textract:DetectDocumentText` - For synchronous OCR text detection
- `textract:AnalyzeDocument` - For advanced document analysis
- `textract:GetDocumentAnalysis` - For retrieving async analysis results
- `textract:GetDocumentTextDetection` - For retrieving async text detection results
- `textract:StartDocumentAnalysis` - For starting async document analysis
- `textract:StartDocumentTextDetection` - For starting async text detection

## API Response Format

### Quality Assessment Response

```json
{
  "status": "SUCCESS",
  "result": {
    "textract_confidence": {
      "geometric_mean": 85.2,
      "arithmetic_mean": 87.1,
      "harmonic_mean": 83.4,
      "minimum": 72.3,
      "maximum": 95.8,
      "median": 86.7,
      "std_deviation": 6.2,
      "total_words": 156
    },
    "quality_assessment": {
      "quality_level": "good",
      "quality_score": 0.8,
      "quality_acceptable": true,
      "confidence_threshold": 60,
      "assessment_summary": "Textract OCR analysis: 85.2% geometric mean confidence. Quality level: good. Total words detected: 156. Confidence range: 72.3%-95.8%"
    },
    "words_data": [
      {
        "text": "Sample",
        "confidence": 95.8,
        "bbox": {
          "left": 0.1,
          "top": 0.2,
          "width": 0.3,
          "height": 0.1
        }
      }
    ]
  },
  "llm_usage": {
    "inputTokens": 0,
    "outputTokens": 0,
    "totalTokens": 0,
    "latencyMs": 1250
  }
}
```

### Integration with Main Processing

The quality assessment is integrated into the main processing pipeline:

```python
# In extract_from_images_thread function
quality_result = assess_document_quality_with_textract(img_bytes)

if quality_result["status"] == ResponseStatus.SUCCESS.value:
    quality_data = quality_result["result"]
    textract_confidence = quality_data["textract_confidence"]
    quality_assessment = quality_data["quality_assessment"]
    
    # Use geometric mean confidence as primary score
    score = textract_confidence["geometric_mean"]
    
    assessment = {
        "page": page_num,
        "score": score,
        "document_quality": {
            "textract_confidence": textract_confidence,
            "quality_assessment": quality_assessment
        },
    }
    quality_assessments.append(assessment)

    # Check if quality is acceptable
    if not quality_assessment["quality_acceptable"] and stop_flag:
        stop_flag.set()
        return {
            "status": StatusMessage.UNCLEAR_DOCUMENT.value,
            "message": f"Page {page_num} failed Textract quality threshold ({quality_assessment['confidence_threshold']}% confidence).",
            "quality_assessments": quality_assessments,
            "llm_usage": thread_llm_usage,
        }
```

## Testing

### Test Script

Use the provided test script to validate the quality assessment:

```bash
python test_textract_quality.py
```

The test script includes:

1. **Confidence Metrics Calculation**: Tests statistical calculations
2. **Image Quality Assessment**: Tests single image processing
3. **PDF Quality Assessment**: Tests multi-page PDF processing
4. **Test Image Generation**: Creates sample images for testing

### Test Output Example

```
🔍 Textract Quality Assessment Testing
==================================================

🧪 Testing Confidence Metrics Calculation
--------------------------------------------------
📊 Confidence Metrics Results:
   Geometric Mean: 87.23% (recommended)
   Arithmetic Mean: 88.75%
   Harmonic Mean: 86.45%
   Min/Max: 78.00% / 95.00%
   Median: 89.00%
   Std Deviation: 5.67%
   Total Words: 8

🧪 Testing Image Quality Assessment: test_document_quality.png
--------------------------------------------------
📄 Image loaded: 45678 bytes
⏱️ Processing time: 1.25 seconds
✅ Quality Assessment Results:
   Quality Level: good
   Quality Score: 0.80
   Quality Acceptable: true
   Confidence Threshold: 60%

📊 Textract Confidence Metrics:
   Geometric Mean: 85.20%
   Arithmetic Mean: 87.10%
   Harmonic Mean: 83.40%
   Min/Max: 72.30% / 95.80%
   Median: 86.70%
   Std Deviation: 6.20%
   Total Words: 156

📝 Assessment Summary:
   Textract OCR analysis: 85.2% geometric mean confidence. Quality level: good. Total words detected: 156. Confidence range: 72.3%-95.8%

🔤 Sample Words (first 10):
    1. 'Test' - 95.8%
    2. 'Document' - 92.3%
    3. 'Quality' - 89.1%
    4. 'Assessment' - 87.5%
    5. 'This' - 91.2%
```

## Migration from LLM-Based Assessment

### Changes Made

1. **Replaced LLM Quality Assessment**
   - Removed `llm_quality` field from extraction results
   - Replaced with `textract_confidence` and `quality_assessment`

2. **Updated Configuration**
   - Added `textract_confidence_threshold` SSM parameter
   - Removed dependency on `document_quality_weights`

3. **Modified Processing Logic**
   - Quality assessment now happens per page during image processing
   - Uses Textract confidence scores instead of LLM evaluation

### Benefits

1. **Cost Reduction**: No additional LLM calls for quality assessment
2. **Consistency**: Same document always gets same quality score
3. **Speed**: Faster processing without LLM quality evaluation
4. **Objectivity**: Based on actual OCR performance metrics
5. **Reliability**: Textract is specifically designed for document analysis

## Error Handling

### Common Error Scenarios

1. **Textract Service Unavailable**
   - Fallback to default quality assessment
   - Log error for monitoring

2. **Empty Document**
   - Returns zero confidence scores
   - Marks as very poor quality

3. **Image Processing Errors**
   - Handles various image formats
   - Converts to PNG for Textract compatibility

4. **IAM Permission Errors**
   - Check if Textract permissions are properly attached
   - Verify role has required Textract actions
   - Common error: "AccessDenied" when calling Textract APIs

### Error Response Format

```json
{
  "status": "FAIL",
  "message": "Textract quality assessment failed: Service unavailable",
  "llm_usage": {
    "inputTokens": 0,
    "outputTokens": 0,
    "totalTokens": 0,
    "latencyMs": 0
  }
}
```

### IAM Troubleshooting

If you encounter IAM permission errors:

1. **Verify Policy Attachment**
   ```bash
   aws iam list-attached-role-policies \
     --role-name ocr-verification-request-processing-lambda-role-{env}
   ```

2. **Check Policy Permissions**
   ```bash
   aws iam get-policy-version \
     --policy-arn arn:aws:iam::{account}:policy/ocr-verification-textract-policy-{env} \
     --version-id v1
   ```

3. **Test Textract Access**
   ```bash
   aws textract detect-document-text \
     --document '{"Bytes": "base64-encoded-image"}' \
     --region ap-southeast-1
   ```

4. **Common IAM Issues**
   - Policy not attached to role
   - Incorrect policy ARN
   - Missing required Textract actions
   - Cross-account permission issues

## Monitoring and Metrics

### Key Metrics to Monitor

1. **Quality Distribution**
   - Percentage of documents by quality level
   - Average confidence scores

2. **Processing Performance**
   - Textract processing time
   - Success/failure rates

3. **Routing Decisions**
   - Documents accepted vs rejected
   - Threshold effectiveness

### CloudWatch Metrics

Consider adding custom CloudWatch metrics for:

- `TextractQualityScore`
- `TextractProcessingTime`
- `QualityAssessmentSuccess`
- `DocumentQualityLevel`

## Best Practices

### Configuration

1. **Threshold Tuning**
   - Start with 60% threshold
   - Adjust based on business requirements
   - Monitor false positive/negative rates

2. **Environment-Specific Settings**
   - Different thresholds for dev/staging/prod
   - A/B testing for threshold optimization

### Performance Optimization

1. **Image Preprocessing**
   - Convert to PNG format for best Textract compatibility
   - Optimize image size for faster processing

2. **Batch Processing**
   - Process multiple pages in parallel where possible
   - Cache results for retry scenarios

### Quality Assurance

1. **Regular Testing**
   - Test with various document types
   - Validate threshold effectiveness
   - Monitor confidence score distributions

2. **Documentation**
   - Keep quality assessment criteria updated
   - Document any threshold changes
   - Maintain test cases for edge scenarios 