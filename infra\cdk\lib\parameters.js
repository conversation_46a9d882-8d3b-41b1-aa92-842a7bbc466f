"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.OcrVerificationParameters = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const ssm = __importStar(require("aws-cdk-lib/aws-ssm"));
class OcrVerificationParameters {
    constructor(scope, id, props) {
        const { stage, resourceNameSuffix, tags = {}, accountId, region } = props;
        const paramPrefix = `/ocr-verification/${stage}`;
        // Environment-specific configurations
        const envConfig = {
            development: {
                s3ArtifactsBucket: `ocr-verification-general-artifacts-${resourceNameSuffix}`,
                s3TrackingBucket: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
                dynamodbTable: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
                queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/ocr-verification`,
                bedrockConfig: {
                    model: "apac.amazon.nova-pro-v1:0",
                    temperature: "0"
                }
            },
            staging: {
                s3ArtifactsBucket: `ocr-verification-general-artifacts-${resourceNameSuffix}`,
                s3TrackingBucket: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
                dynamodbTable: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
                queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/ocr-verification`,
                bedrockConfig: {
                    model: "apac.amazon.nova-pro-v1:0",
                    temperature: "0"
                }
            },
            production: {
                s3ArtifactsBucket: `ocr-verification-general-artifacts-${resourceNameSuffix}`,
                s3TrackingBucket: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
                dynamodbTable: `ocr-verification-requests-tracking-${resourceNameSuffix}`,
                queueUrlBase: `https://sqs.${region}.amazonaws.com/${accountId}/ocr-verification`,
                bedrockConfig: {
                    model: "apac.amazon.nova-pro-v1:0",
                    temperature: "0"
                }
            }
        };
        // Select the appropriate configuration
        const config = envConfig[stage] || envConfig.staging;
        // Helper function to create parameter with tags
        const createParameterWithTags = (id, options) => {
            const parameter = new ssm.StringParameter(scope, id, options);
            // Apply tags to each parameter
            if (tags) {
                Object.entries(tags).forEach(([key, value]) => {
                    cdk.Tags.of(parameter).add(key, value);
                });
            }
            return parameter;
        };
        // Bedrock Region parameter
        createParameterWithTags('BedrockRegionParam', {
            parameterName: `${paramPrefix}/bedrock/region`,
            stringValue: 'ap-southeast-1', //need to revert when quota increases
            description: 'Bedrock region parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // Bedrock inference config parameter
        createParameterWithTags('BedrockInferenceConfigParam', {
            parameterName: `${paramPrefix}/bedrock/inference/config`,
            stringValue: JSON.stringify({
                max_attempts: "2",
                bedrock_model_name: config.bedrockConfig.model,
                model_version: "bedrock-2023-05-31",
                bedrock_temperature: config.bedrockConfig.temperature,
                bedrock_max_tokens: "8192",
                bedrock_top_p: "1",
                bedrock_top_k: "50",
                input_token_cost: "0.0008",
                output_token_cost: "0.0032"
            }),
            description: 'Bedrock inference config parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // Gemini API Key parameter - Set manually via AWS Console or CLI
        // This parameter is not managed by CDK for security reasons
        // Use: aws ssm put-parameter --name "/ocr-verification/{stage}/gemini/api/key" --value "your-api-key" --type "SecureString" --overwrite
        // Gemini inference config parameter
        createParameterWithTags('GeminiInferenceConfigParam', {
            parameterName: `${paramPrefix}/gemini/inference/config`,
            stringValue: JSON.stringify({
                max_output_tokens: 2000,
                temperature: 0.0,
                top_p: 0.95,
                candidate_count: 1
            }),
            description: 'Gemini inference configuration parameters',
            tier: ssm.ParameterTier.STANDARD,
        });
        // LLM Provider selector parameter
        createParameterWithTags('LlmProviderParam', {
            parameterName: `${paramPrefix}/llm/provider`,
            stringValue: 'gemini', // Default to gemini, can be switched to 'bedrock'
            description: 'LLM provider selection (bedrock or gemini)',
            tier: ssm.ParameterTier.STANDARD,
        });
        // S3 bucket parameter for artifacts
        createParameterWithTags('S3ArtifactsBucketParam', {
            parameterName: `${paramPrefix}/s3/artifacts`,
            stringValue: config.s3ArtifactsBucket,
            description: 'S3 bucket parameter for artifacts',
            tier: ssm.ParameterTier.STANDARD,
        });
        // S3 bucket prompts directory parameter
        createParameterWithTags('S3ArtifactsPromptsDirParam', {
            parameterName: `${paramPrefix}/s3/artifacts/prompts`,
            stringValue: 'prompts/',
            description: 'S3 bucket prompts directory parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // S3 bucket for tracking requests parameter
        createParameterWithTags('S3TrackingBucketParam', {
            parameterName: `${paramPrefix}/s3/tracking`,
            stringValue: config.s3TrackingBucket,
            description: 'S3 bucket for tracking requests parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // SQS notification queue parameter
        createParameterWithTags('SQSNotificationQueueParam', {
            parameterName: `${paramPrefix}/queue/notification`,
            stringValue: `${config.queueUrlBase}-notification-queue-${resourceNameSuffix}`,
            description: 'SQS notification queue parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // Dynamodb table parameter
        createParameterWithTags('DynamoDBTableParam', {
            parameterName: `${paramPrefix}/dynamodb/table`,
            stringValue: config.dynamodbTable,
            description: 'Dynamodb table parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // Dynamodb ttl parameter
        createParameterWithTags('DynamoDBTtlParam', {
            parameterName: `${paramPrefix}/dynamodb/ttl`,
            stringValue: '48',
            description: 'Dynamodb ttl parameter',
            tier: ssm.ParameterTier.STANDARD,
        });
        // LLM images process limit
        createParameterWithTags('ImagesLimitParam', {
            parameterName: `${paramPrefix}/bedrock/maximage/size`,
            stringValue: '13',
            description: 'LLM images process limit',
            tier: ssm.ParameterTier.STANDARD,
        });

        // TEMPORARY: Document quality parameters (to be removed after import)
        createParameterWithTags('DocumentQualityThresholdScore', {
            parameterName: `${paramPrefix}/document-quality/score/threshold`,
            stringValue: '0.5',
            description: 'Document quality threshold score',
            tier: ssm.ParameterTier.STANDARD,
        });

        createParameterWithTags('DocumentQualityScoreWeight', {
            parameterName: `${paramPrefix}/document-quality/score/weights`,
            stringValue: JSON.stringify({
                text_clarity: 0.4,
                contrast_readability: 0.2,
                resolution_text_size: 0.3,
                overall_image_quality: 0.1,
            }),
            description: 'Document Quality weights for each category',
            tier: ssm.ParameterTier.STANDARD,
        });


        // Textract confidence threshold parameter
        createParameterWithTags('TextractConfidenceThreshold', {
            parameterName: `${paramPrefix}/textract/confidence/threshold`,
            stringValue: '60',
            description: 'Textract confidence threshold for document quality assessment',
            tier: ssm.ParameterTier.STANDARD,
        });
        // Supported Document Types parameter
        createParameterWithTags('SupportedDocumentTypes', {
            parameterName: `${paramPrefix}/supported/document_types`,
            stringValue: "EJARI,FORM-F",
            description: 'Supported Document Types',
            tier: ssm.ParameterTier.STANDARD,
        });
    }
}
exports.OcrVerificationParameters = OcrVerificationParameters;
//# sourceMappingURL=data:application/json;base64,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