name: deploy-staging

on:
  issue_comment:
    types: [ created ] # will deploy only if a comment is made on a PR.

permissions:
  id-token: write # required to use OIDC authentication
  contents: write # required to checkout the code from the repo & for Branch Deployment
  pull-requests: write # required for Branch Deployment
  deployments: write # required for Branch Deployment
  checks: read # required for Branch Deployment
  statuses: read # required for Branch Deployment

jobs:
  deploy-staging:
    uses: Propertyfinder/github-actions-templates/.github/workflows/cdk-deploy-staging.yml@v1.0.0
    with:
      go_version: 1.23
    secrets:
      cdk_default_account: ${{ secrets.CORE_PLATFORM_STAGING_AWS_ACCOUNT_ID }}
      rw_github_token: ${{ secrets.RW_GITHUB_TOKEN }}
      submodules_token: ${{ secrets.SUBMODULES_TOKEN }}
      slack_webhook_url: ${{ secrets.GHA_NOTIFICATION_SLACK_WEBHOOK_URL }}
