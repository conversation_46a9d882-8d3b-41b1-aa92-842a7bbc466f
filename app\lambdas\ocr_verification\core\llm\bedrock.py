import os

import boto3
from botocore.config import Config
from config.config import config
from core.logger.logger import (
    get_logger,
    log_frame_info,
    log_frame_debug,
    log_frame_error,
    log_frame_warning,
)
from core.utils.utils import get_json_value
from core.enums.enums import ResponseStatus

# Load configuration object containing SSM parameters
config_obj = config.ssm_config

# Initialize logger for the current module
logger = get_logger(__name__)

# Load AWS test credentials from environment variables (used for test/local environments)
# These are optional and allow for local testing with specific AWS credentials
AWS_ACCESS_KEY_ID_TEST = os.getenv("AWS_ACCESS_KEY_ID_TEST", None)
AWS_SECRET_ACCESS_KEY_TEST = os.getenv("AWS_SECRET_ACCESS_KEY_TEST", None)
AWS_SESSION_TOKEN_TEST = os.getenv("AWS_SESSION_TOKEN_TEST", None)

# Ensure config_obj is a dictionary
if not isinstance(config_obj, dict):
    log_frame_warning(logger, "config_obj is not a dictionary, using environment variables and defaults")
    config_obj_safe = {}
else:
    config_obj_safe = config_obj

# Determine Bedrock region from config or fallback to AWS default region
bedrock_region = config_obj_safe.get("bedrock_model_region") or os.getenv("DEFAULT_REGION", "us-east-1")

log_frame_info(logger, f"🏗️ Bedrock Configuration:")
log_frame_info(logger, f"  🌍 Bedrock region: {bedrock_region}")
log_frame_info(logger, f"  📋 Raw bedrock config: {config_obj_safe.get('bedrock_inference_config')}")

# Get retry attempt count from config or use default value of 2
bedrock_max_attempts = int(
    get_json_value(config_obj_safe.get("bedrock_inference_config"), "max_attempts") or 2
)

log_frame_info(logger, f"  🔄 Max attempts: {bedrock_max_attempts}")

bedrock_config = Config(
    retries={"total_max_attempts": bedrock_max_attempts, "mode": "standard"},
)

# Create a Boto3 session with test credentials if provided; otherwise, use default credentials
# This allows for flexible authentication in different environments
if AWS_ACCESS_KEY_ID_TEST and AWS_SECRET_ACCESS_KEY_TEST and AWS_SESSION_TOKEN_TEST:
    bedrock_session = boto3.Session(
        aws_access_key_id=AWS_ACCESS_KEY_ID_TEST,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY_TEST,
        aws_session_token=AWS_SESSION_TOKEN_TEST,
        region_name=bedrock_region,
    )
else:
    bedrock_session = boto3.Session(region_name=bedrock_region)

bedrock_runtime = bedrock_session.client("bedrock-runtime", config=bedrock_config)


class BedrockLlm:
    """
    A class for interacting with AWS Bedrock LLM models.
    Provides methods to send messages to Bedrock models and handle responses.
    """

    @classmethod
    def converse_model(cls, messages=[]):
        """
        Send messages to a Bedrock LLM model and get a response.

        Args:
            messages: List of message objects to send to the model

        Returns:
            Dictionary containing:
            - status: Response status (SUCCESS, THROTTLE, or FAIL)
            - result: Model's text response (if successful)
            - llm_usage: Usage metrics and costs (if available)
            - message: Error message (if failed)
            - data: Additional data (usually None for errors)
        """
        try:
            # Extract model ID from configuration
            model_id = str(
                get_json_value(
                    config_obj_safe.get("bedrock_inference_config"), "bedrock_model_name"
                )
            )
            
            log_frame_info(logger, f"🏗️ Bedrock Model Configuration:")
            log_frame_info(logger, f"  📋 Raw inference config: {config_obj_safe.get('bedrock_inference_config')}")
            log_frame_info(logger, f"  🎯 Selected model ID: '{model_id}'")

            # Extract configuration parameters with logging
            max_tokens_raw = get_json_value(config_obj_safe.get("bedrock_inference_config"), "bedrock_max_tokens") or "4096"
            max_tokens = int(max_tokens_raw)
            log_frame_info(logger, f"  📊 Max tokens: {max_tokens} (from SSM: '{max_tokens_raw}')")
            
            temperature_raw = get_json_value(config_obj_safe.get("bedrock_inference_config"), "bedrock_temperature") or "0"
            temperature = int(temperature_raw)
            log_frame_info(logger, f"  🌡️ Temperature: {temperature} (from SSM: '{temperature_raw}')")
            
            top_p_raw = get_json_value(config_obj_safe.get("bedrock_inference_config"), "bedrock_top_p") or "1"
            top_p = int(top_p_raw)
            log_frame_info(logger, f"  🎲 Top-p: {top_p} (from SSM: '{top_p_raw}')")

            # Prepare inference configuration with model parameters
            inferenceconfig = {
                # Maximum number of tokens to generate
                "maxTokens": max_tokens,
                # Controls randomness (0 = deterministic, higher = more random)
                "temperature": temperature,
                # Controls nucleus sampling (probability mass cutoff)
                "topP": top_p,
            }

            # Build the complete request payload for Bedrock API
            request_payload = {
                "modelId": model_id,
                "inferenceConfig": inferenceconfig,
                "messages": messages,
            }

            log_frame_debug(
                logger,
                message="Model request payload:",
            )

            response = bedrock_runtime.converse(**request_payload)

            # Extract the response message from the nested response structure
            response_message = response["output"]["message"]
            result = response_message["content"][0]["text"]

            # Extract usage information if available (tokens used, costs, etc.)
            llm_usage = response["usage"] if "usage" in response else None

            # If metrics are available, merge them with usage info for comprehensive tracking
            if llm_usage:
                llm_usage.update(response["metrics"])

            log_frame_info(
                logger,
                message="Model converse successful. Response preview:",
                result=result[:200],
            )
            log_frame_debug(logger, message=f"LLM usage {llm_usage}")

            return {
                "status": ResponseStatus.SUCCESS.value,
                "result": result,
                "llm_usage": llm_usage,
            }

        except bedrock_runtime.exceptions.ThrottlingException as e:
            log_frame_error(logger, message="Bedrock model throttled:", error=str(e))
            return {
                "status": ResponseStatus.THROTTLE.value,
                "message": "Unable to process the request due to Throttling error",
                "data": None,
            }
        except Exception as e:  # pylint: disable=broad-except
            log_frame_error(
                logger, message="Error invoking Bedrock model:", error=str(e)
            )
            return {
                "status": ResponseStatus.FAIL.value,
                "message": f"ERROR: {str(e)}",
                "data": None,
            }
